import IndexBase from 'fc/stores/IndexBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import Medicao from '~/domains/geoObras/Medicao';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import MedicaoService from '~/services/geoObras/MedicaoService';

class MedicaoIndexStore extends IndexBase {
  @observable medicoes;
  @observable ultimaMedicaoObra;
  obraId;

  constructor(idObra) {
    super(MedicaoService, Medicao, 'id', 'desc');
    this.fileStore = new MultipleFileUploaderStore(
      null,
      [],
      (file) => MedicaoService.upload(idObra, file),
      (key) => this.downloadArquivo(key),
      (idArquivo) => this.removerArquivoMedicao(idArquivo),
      (idArquivo, arquivoDTO) => this.atualizarArquivoMedicao(idArquivo, arquivoDTO)
    );
    this.obraId = idObra;
    this.carregarPorObra = this.carregarPorObra.bind(this);
    makeObservable(this);
  }

  @action
  setIdObra(id) {
    this.idObra = id;
    this.obraId = id;
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.pagination = parameters;
    this.service
      .advancedSearch(parameters)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items ?? []);
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }

  @action
  carregarPorObra(idObra, callback) {
    this.loading = true;
    MedicaoService.carregarPorObra(idObra)
      .then((response) =>
        runInAction(() => {
          this.medicoes = response.data;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.setLoading(false);
        })
      );
  }

  downloadArquivo(key) {
    const arquivo = this.fileStore.keyedUploadedFiles.find((f) => f.key === key);
    return MedicaoService.download(this.obra.id, arquivo.arquivo);
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'percentualConclusao',
        label: 'Percentual de Conclusão',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'dataInicio',
        label: 'Data Inicial',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataFim',
        label: 'Data Final',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
    ];
  }

  getFilterSuggest() {
    const filterSuggest = [];
    filterSuggest.push({
      id: '',
      field: 'obra',
      operator: 'EQUAL_TO',
      value: this.idObra,
      formatted: '',
      fixed: true,
      invisible: true,
      completeParam: {
        field: 'obra',
        label: 'obra',
        type: SearchTypes.NUMBER,
      },
    });
    return filterSuggest;
  }

  @action
  recuperarUltimaMedicaoObra(obraId, callback) {
    if (obraId) {
      this.service
        .getUltimaMedicaoObra(obraId)
        .then((response) => {
          runInAction(() => {
            this.ultimaMedicaoObra = response.data;
            callback && callback();
          });
        })
        .catch((e) => {
          showErrorNotification('Erro ao buscar a última medição da obra.', e);
          return null;
        });
    }
  }

  hasRequisicaoModificacao(obraId, callback) {
    if (obraId) {
      this.service
        .hasRequisicaoModificacao(obraId)
        .then((response) => {
          runInAction(() => {
            callback && callback(response.data);
          });
        })
        .catch((e) => {
          showErrorNotification('Erro ao buscar a existência de uma requisição de modificação.', e);
          return null;
        });
    }
  }
}

export default MedicaoIndexStore;
