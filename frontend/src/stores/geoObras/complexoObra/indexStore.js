import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import IndexBase from 'fc/stores/IndexBase';
import ComplexoObraService from '~/services/geoObras/ComplexoObraService';
import ComplexoObra from '~/domains/geoObras/ComplexoObra';
import { makeObservable } from 'mobx';

class ComplexoObrasIndexStore extends IndexBase {
  constructor() {
    super(ComplexoObraService, ComplexoObra, 'id', 'desc');
    makeObservable(this);
  }

  getFilterSuggest() {
    const filterSuggest = [];

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    let searchParams = [];

    searchParams.push(
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'descricao',
        label: 'Descrição',
        type: SearchTypes.TEXT,
      },
      {
        field: 'quantidadeObrasAssociadas',
        label: 'Quantidade de Obras Associadas',
        type: SearchTypes.NUMBER,
      }
    );
    return searchParams;
  }
}

export default ComplexoObrasIndexStore;
