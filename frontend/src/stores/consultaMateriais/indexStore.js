import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import IndexBase from 'fc/stores/IndexBase';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import MaterialService from '~/services/MaterialService';
import SecaoCatalogoService from '~/services/SecaoCatalogoService';
import DivisaoService from '~/services/DivisaoService';
import GrupoService from '~/services/GrupoService';
import ClasseService from '~/services/ClasseService';
import SubClasseService from '~/services/SubClasseService';
import { action, observable, override, runInAction } from 'mobx';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { DATE_FORMAT_REPORT } from 'fc/utils/date';
import moment from 'moment';
import Classe from '~/domains/Classe';
import SubClasse from '~/domains/SubClasse';
import Material from '~/domains/Material';
import Grupo from '~/domains/Grupo';
import Divisao from '~/domains/Divisao';
import SecaoCatalogo from '~/domains/SecaoCatalogo';

class ConsultaMateriaisIndexStore extends IndexBase {
  @observable listMateriais = [];
  @observable paginationMateriais = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  @observable listServicos = [];
  @observable paginationServicos = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  constructor() {
    super(MaterialService, Material);
    this.copyUrl = this.copyUrl.bind(this);
  }

  exportCSV(csv) {
    const blob = new Blob([csv], {
      type: 'application/csv;charset=utf-8;',
    });

    const CSV_EXTENSION = '.csv';
    const timestamp = moment().format(DATE_FORMAT_REPORT);
    const filename = 'CATALOGO_DE_PRODUTOS_' + timestamp + CSV_EXTENSION;

    const link = document.createElement('a');
    link.setAttribute('href', URL.createObjectURL(blob));
    link.setAttribute('download', filename);
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  getCSV(filter) {
    this.loading = true;
    const parameters =
      filter === 'servicos'
        ? this.paginationServicos
        : filter === 'materiais'
        ? this.paginationMateriais
        : this.pagination;
    this.service
      .getCSV(parameters)
      .then((response) =>
        runInAction(() => {
          response.data ? this.exportCSV(response.data) : showErrorNotification('Ocorreu um erro!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  initialize(id, callback) {
    this.loading = true;
    this.service
      .getById(id)
      .then((response) =>
        runInAction(() => {
          this.object = response.data;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  copyUrl(url) {
    try {
      const storage = document.createElement('textarea');
      storage.value = url;
      document.body.appendChild(storage);
      storage.select();
      document.execCommand('copy');
      document.body.removeChild(storage);

      showNotification('success', 'Link copiado para área de transferência');
    } catch (e) {
      console.error(e);
      showNotification('error', null, 'Não foi possível copiar o link');
    }
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'CODIGO_CLASSE',
        label: 'Classe',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Classe, ClasseService, 'descricao', 'codigo'),
        value: 'codigo',
        filters: [{ key: 'grupo', value: 'CODIGO_GRUPO' }],
      },
      {
        field: 'CODIGO_SUBCLASSE',
        label: 'SubClasse',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(SubClasse, SubClasseService, 'descricao', 'codigo'),
        value: 'codigo',
        filters: [{ key: 'classe', value: 'CODIGO_CLASSE' }],
      },
      {
        field: 'CODIGO_GRUPO',
        label: 'Grupo',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Grupo, GrupoService, 'descricao', 'codigo'),
        value: 'codigo',
        filters: [{ key: 'divisao', value: 'CODIGO_DIVISAO' }],
      },
      {
        field: 'CODIGO_DIVISAO',
        label: 'Divisão',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Divisao, DivisaoService, 'descricao', 'codigo'),
        value: 'codigo',
        filters: [{ key: 'secao', value: 'CODIGO_SECAO' }],
      },
      {
        field: 'CODIGO_SECAO',
        label: 'Seção',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(SecaoCatalogo, SecaoCatalogoService, 'descricao', 'codigo'),
        value: 'codigo',
      },
      {
        field: 'CODIGO_MATERIAL',
        label: 'Código',
        type: SearchTypes.TEXT,
      },
    ];
  }

  getElasticParams(params) {
    return {
      search: params?.andParameters?.find((param) => param.field === 'query')?.value ?? '',
      filters: params?.andParameters?.filter((param) => param.field !== 'query'),
      page: params?.page,
    };
  }

  @override
  load(options = {}, filter, callback) {
    this.loading = true;

    const advancedSearchParams = JSON.parse(JSON.stringify(this.advancedSearchParams));
    const parameters = Object.assign(advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    this.advancedSearchParams = advancedSearchParams;

    const promises = [];
    if (!filter || filter === 'todos') {
      this.pagination = parameters;
      const parametersMateriais = JSON.parse(JSON.stringify(parameters));
      parametersMateriais.andParameters.push({
        field: 'STATUS_MATERIAL',
        operator: 'EQUAL_TO',
        value: 'A',
      });
      const elasticParams = this.getElasticParams(parametersMateriais);
      promises.push(this.service.elasticSearch(elasticParams));
    }

    if (!filter || filter === 'materiais') {
      const parametersMateriais = JSON.parse(JSON.stringify(parameters));
      parametersMateriais.andParameters.push({
        field: 'TIPO',
        value: 'M',
      });
      parametersMateriais.andParameters.push({
        field: 'STATUS_MATERIAL',
        operator: 'EQUAL_TO',
        value: 'A',
      });
      this.paginationMateriais = parametersMateriais;
      const elasticParams = this.getElasticParams(parametersMateriais);
      promises.push(this.service.elasticSearch(elasticParams));
    }

    if (!filter || filter === 'servicos') {
      const parametersServicos = JSON.parse(JSON.stringify(parameters));
      parametersServicos.andParameters.push({
        field: 'TIPO',
        value: 'S',
      });
      parametersServicos.andParameters.push({
        field: 'STATUS_MATERIAL',
        operator: 'EQUAL_TO',
        value: 'A',
      });
      this.paginationServicos = parametersServicos;
      const elasticParams = this.getElasticParams(parametersServicos);
      promises.push(this.service.elasticSearch(elasticParams));
    }

    Promise.all(promises)
      .then((responses) =>
        runInAction(() => {
          if (filter === 'todos') {
            this.pagination.total = responses[0].data.total;
            this.list = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'materiais') {
            this.paginationMateriais.total = responses[0].data.total;
            this.listMateriais = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'servicos') {
            this.paginationServicos.total = responses[0].data.total;
            this.listServicos = this.initializeLoadedList(responses[0].data.items);
          } else {
            this.pagination.total = responses[0].data.total;
            this.list = this.initializeLoadedList(responses[0].data.items);

            this.paginationMateriais.total = responses[1].data.total;
            this.listMateriais = this.initializeLoadedList(responses[1].data.items);

            this.paginationServicos.total = responses[2].data.total;
            this.listServicos = this.initializeLoadedList(responses[2].data.items);
          }
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @override
  setPagination(pagination, filter) {
    if (!filter || filter === 'materiais') this.paginationMateriais = JSON.parse(JSON.stringify(pagination));
    if (!filter || filter === 'servicos') this.paginationServicos = JSON.parse(JSON.stringify(pagination));
    if (!filter || filter === 'todos') this.pagination = JSON.parse(JSON.stringify(pagination));
  }
}

export default ConsultaMateriaisIndexStore;
