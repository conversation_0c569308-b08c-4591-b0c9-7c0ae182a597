import { observable, action, runInAction } from 'mobx';
import Inexigibilidade from '~/domains/Inexigibilidade';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import FormBase from 'fc/stores/FormBase';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import LicitanteIndexStore from '../licitante/indexStore';
import FonteRecursoService from '~/services/FonteRecursoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TermoIndexStore from '../licitacao/termoIndex';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import FundamentacaoLegalService from '~/services/FundamentacaoLegalService';
import VencedoresFormStore from '../vencedores/formStore';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import moment from 'moment';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import AccessPermission from '~/constants/AccessPermission';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import FonteRecurso from '~/domains/FonteRecurso';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import ObraTipo from '~/domains/ObraTipo';
import ObraCategoria from '~/domains/ObraCategoria';
import ObraTipoService from '~/services/ObraTipoService';
import ObraCategoriaService from '~/services/ObraCategoriaService';
import EdificacaoService from '~/services/EdificacaoService';

class InexigibilidadeFormStore extends FormBase {
  @observable fileStore;
  @observable arquivoInexigibilidadeList = [];
  @observable idInexigibilidade;
  @observable vencedoresStore;
  @observable enableReqMod = false;
  @observable anos = [];
  @observable entidadesFiltradas;
  @observable arquivosTdaInexigibilidade = [];
  @observable fontesRecursos = [];
  @observable fundamentacoesLegais = [];
  @observable loadingTabDadosBasicos = false;
  @observable obraObject = {};
  @observable edificacao;
  @observable stateObra = {};
  @observable idAlerta = null;
  @observable responsaveisInexigibilidade;
  @observable entidadeDestino = '';
  @observable dataTransferencia = '';
  @observable transferido = false;

  constructor() {
    super(InexigibilidadeService, Inexigibilidade);
    this.entidadeSelectStore = new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id');
    this.fontesRecursoStore = new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.fornecedoresIndexStore = new LicitanteIndexStore();
    this.termoIndexStore = new TermoIndexStore();
    this.orgaosParticipantesStore = new AsyncMultiselectStore(Entidade, EntidadeService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
      andParameters: [{ field: 'id', operator: 'NOT_EQUAL_TO', value: AppStore.getContextEntity()?.id }],
    });
    this.obraTipoStore = new AsyncDropDownStore(ObraTipo, ObraTipoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.obraCategoriaStore = new AsyncDropDownStore(ObraCategoria, ObraCategoriaService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.fileStore = new MultipleFileUploaderStore(
      null,
      DadosEstaticosService.getTipoArquivoInexigibilidade(),
      (file) => InexigibilidadeService.upload(file),
      (fileDTO, countDownload) => InexigibilidadeService.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoInexigibilidade(idArquivo),
      (idArquivo, arquivoInexigibilidadeDTO) =>
        this.atualizarArquivoInexigibilidade(idArquivo, arquivoInexigibilidadeDTO)
    );

    this.fileStoreTda = new MultipleFileUploaderStore(
      null,
      [],
      (file) => TdaInexigibilidadeService.upload(file),
      (fileDTO) => TdaInexigibilidadeService.download(fileDTO)
    );

    this.inexigibilidadeService = InexigibilidadeService;

    this.onSelectFornecedores = this.onSelectFornecedores.bind(this);
    this.criarInexigibilidade = this.criarInexigibilidade.bind(this);
    this.removerArquivoInexigibilidade = this.removerArquivoInexigibilidade.bind(this);
    this.atualizarArquivoInexigibilidade = this.atualizarArquivoInexigibilidade.bind(this);
    this.getEntidadesFiltradas = this.getEntidadesFiltradas.bind(this);
    this.setInexigibilidade = this.setInexigibilidade.bind(this);

    this.loadTipos();
    this.vencedoresStore = new VencedoresFormStore();
  }

  checkDataCadastro(dataCadastro) {
    const data = dataCadastro ?? this.object?.dataCadastro;
    if (data) {
      const dataCadastro = moment(data);
      const current = moment();
      const diff = moment.duration(current.diff(dataCadastro));

      this.enableReqMod = diff.asHours() > 24;
    }
  }

  setArquivoInexigibilidadeList(arquivoInexigibilidadeList) {
    this.arquivoInexigibilidadeList = arquivoInexigibilidadeList;
  }

  fundamentacaoLegalFilter(lei) {
    return {
      page: { index: 1, size: 100 },
      sort: {
        by: 'fundamentacao',
        order: 'asc',
      },
      andParameters: [
        { field: 'legislacao', operator: SearchOperators.EQUAL_TO.value, value: lei },
        { field: 'ativo', operator: SearchOperators.EQUAL_TO.value, value: true },
        { field: 'tipoDispensa', operator: SearchOperators.EQUAL_TO.value, value: false },
      ],
    };
  }

  @action
  initializeTabDadosBasicos(callback) {
    const filtro = this.fundamentacaoLegalFilter('LEI_N_14133');
    this.loadingTabDadosBasicos = true;
    Promise.all([
      FonteRecursoService.getAll(),
      FundamentacaoLegalService.advancedSearch(filtro),
      InexigibilidadeService.getAnosInexigibilidade(),
    ])
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response[0].data;
          this.fundamentacoesLegais = response[1].data.items;
          this.anos = response[2].data.map((ano) => ({ text: ano.toString(), value: ano }));
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loadingTabDadosBasicos = false;
        callback && callback();
      });
  }

  @action
  loadFundamentacaoLegal(lei, callback) {
    const filtro = this.fundamentacaoLegalFilter(lei);
    this.loadingTabDadosBasicos = true;
    FundamentacaoLegalService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          this.fundamentacoesLegais = response.data.items;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() => {
        this.loadingTabDadosBasicos = false;
        callback && callback();
      });
  }

  @action
  loadTipos(lei, callback) {
    let legislacao;
    if (lei) {
      legislacao = lei;
    } else {
      legislacao = this.inexigibilidade?.processoMigrado
        ? 'LEI_N_8666'
        : this.inexigibilidade?.lei
        ? this.inexigibilidade?.lei
        : 'LEI_N_14133';
    }
    const filtro = {
      tipoProcesso: 'INEXIGIBILIDADE',
      filtros:
        this.object?.naturezasDoObjeto && this.object?.naturezasDoObjeto.includes('OBRAS')
          ? [legislacao, 'NATUREZAOBJ_OBRA']
          : [legislacao],
    };
    ObrigatoriedadeArquivoService.getArquivosObrigatorios(filtro)
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoInexigibilidade().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });

          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  isLegislacaoAntiga() {
    return this.object.lei === 'LEI_N_8666';
  }

  isProcessoMigrado() {
    return this.object.processoMigrado;
  }

  getFilterSuggestTermo() {
    const filterSuggest = [
      {
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
      {
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    return filterSuggest;
  }

  @action
  initializeArquivos(idInexigibilidade, callback) {
    if (idInexigibilidade) {
      const promisses = [];
      promisses.push(this.service.getById(idInexigibilidade));
      promisses.push(this.service.recuperarArquivos(idInexigibilidade));

      Promise.all(promisses)
        .then((response) => {
          const arquivosRecuperados = response[1].data;
          this.fileStore.initialize(arquivosRecuperados);
          this.arquivoInexigibilidadeList = arquivosRecuperados;
          this.inexigibilidade = response[0].data;
          this.loadTipos();

          callback && callback();
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  atualizaFornecedores(fornecedores) {
    const removidos = this.object.fornecedores?.filter(
      (forn) => !fornecedores.map((f) => f.id).includes(forn.licitante.id)
    );
    this.object.licitantes = fornecedores;
    removidos.forEach((f) => this.removeFornecedor(f.licitante));
  }

  checkFornecedorIsVencedor(fornecedor) {
    return this.object.fornecedores?.some((f) => f.licitante.id == fornecedor.id);
  }

  @action
  removeFornecedor(fornecedor) {
    this.object.licitantes = this.object.licitantes.filter((l) => l.id !== fornecedor.id);
    this.object.fornecedores = this.object.fornecedores?.filter((f) => f.licitante.id !== fornecedor.id);
    this.updateLicitantesFornecedoresVencedoresStore();
  }

  initializeTdaInexigibilidade(idInexigibilidade) {
    if (idInexigibilidade && this.hasPermissionTda()) {
      TdaInexigibilidadeService.tdaInexigibilidadeByIdInexigibilidade(idInexigibilidade)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaInexigibilidade = response.data;
              this.carregaArquivosTda(tdaInexigibilidade);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  carregaArquivosTda(tdaInexigibilidade) {
    if (tdaInexigibilidade) {
      TdaInexigibilidadeService.recuperarArquivos(tdaInexigibilidade.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaInexigibilidade.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.arquivosTdaInexigibilidade = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  updateLicitantesFornecedoresVencedoresStore() {
    this.vencedoresStore.licitantes = this.object.licitantes;
    this.vencedoresStore.vencedores = this.object.fornecedores;
  }

  @action
  initializeVencedorStore() {
    const idsUnicos = new Set();
    const licitantes = this.object.fornecedores
      .map((f) => f.licitante)
      .filter((licitante) => {
        if (idsUnicos.has(licitante.id)) {
          return false;
        } else {
          idsUnicos.add(licitante.id);
          return true;
        }
      });

    this.object.licitantes = licitantes;

    this.vencedoresStore.initialize(
      this.object.termoReferencia,
      this.object.licitantes ?? [],
      this.object.fornecedores ?? [],
      [],
      [],
      this.object.tipoAdjudicacao,
      (fornecedores) => {
        this.updateAttribute('fornecedores', fornecedores);
        !this.isProcessoMigrado() && this.setValor(this.vencedoresStore.getValorTotal());
      },
      () => {},
      () => {},
      (tipoAdjudicacao) => {
        this.updateAttribute('tipoAdjudicacao', tipoAdjudicacao);
      },
      this.object.processoMigrado
    );
    !this.object.valor && this.setValor(this.vencedoresStore.getValorTotal(this.isLegislacaoAntiga()));
  }

  @action
  setValor(valor) {
    this.object.valor = valor;
  }

  onSelectFornecedores(fornecedores) {
    this.updateAttribute('fornecedores', fornecedores);
  }

  criarInexigibilidade(callback) {
    this.loading = true;
    const inexigibilidadeDTO = {
      id: this.object.id,
      inexigibilidade: this.object,
      arquivosInexigibilidade: this.arquivoInexigibilidadeList,
      obra: this.obraObject && Object.keys(this.obraObject).length > 0 ? this.obraObject : null,
      edificacao: this.edificacao,
    };
    this.inexigibilidadeService
      .criarInexigibilidade(inexigibilidadeDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Inexigibilidade criada com sucesso!');
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  salvarTransferenciaEntidade(inexigibilidade, callback) {
    const inexigibilidadeDTO = {
      id: this.object.id,
      inexigibilidade: {
        ...inexigibilidade,
      },
    };
    this.loading = true;
    this.service
      .salvarTransferenciaEntidade(inexigibilidadeDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Transferência de entidade salva com sucesso!');
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivosInexigibilidade: this.arquivoInexigibilidadeList, lei: this.object.lei })
      .then(() => runInAction(() => callback && callback()))
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  removerArquivoInexigibilidade(idArquivo) {
    return InexigibilidadeService.removerArquivo(this.idInexigibilidade, idArquivo);
  }

  atualizarArquivoInexigibilidade(idArquivo, arquivoInexigibilidadeDTO) {
    return InexigibilidadeService.atualizarArquivo(this.idInexigibilidade, idArquivo, arquivoInexigibilidadeDTO);
  }

  @action
  setArquivoInexigibilidadeList(arquivoInexigibilidadeList) {
    this.arquivoInexigibilidadeList = arquivoInexigibilidadeList;
  }

  @action
  setUploadedFiles(arquivos) {
    this.fileStore.uploadedFiles = arquivos.map((arquivo) => {
      const diretorioDividido = arquivo.diretorio.split('\\');
      const nomeArquivo = diretorioDividido[diretorioDividido.length - 1].split('.');
      arquivo.arquivo = {
        lookupId: nomeArquivo[0],
        nomeOriginal: arquivo.nome,
        tipoArquivo: `application/${nomeArquivo[1]}`,
      };
      return arquivo;
    });
  }

  @action
  updateLicitantes() {
    const licitantes = this.object.fornecedores?.map((f) => f.licitante);
    this.object.licitantes = licitantes;
    this.vencedoresStore.setLicitantes(licitantes);
  }

  @action
  getPessoasResponsaveis(callback) {
    const entidade = AppStore.getContextEntity();
    entidade &&
      this.service
        .getPessoasResponsaveis(entidade?.id)
        .then((response) =>
          runInAction(() => {
            this.responsaveisInexigibilidade = response.data;
            if (
              this.object.idResponsavelInexigibilidade &&
              this.object.responsavelInexigibilidade &&
              !this.responsaveisInexigibilidade.some(
                (pessoa) =>
                  pessoa.id == this.object.idResponsavelInexigibilidade &&
                  pessoa.nome == this.object.responsavelInexigibilidade
              )
            ) {
              this.responsaveisInexigibilidade?.push({
                nome: this.object.responsavelInexigibilidade,
                id: this.object.idResponsavelInexigibilidade,
              });
            }
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
  }

  @action
  updateResponsavelInexigibilidadeAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.object[attribute] = value;
    const nomePessoaResponsavel = this.responsaveisInexigibilidade.find((pessoa) => pessoa.id == value)?.nome;
    this.object['responsavelInexigibilidade'] = nomePessoaResponsavel;
  }

  rulesDefinition() {
    const isObra = ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].some((natureza) =>
      this.object?.naturezasDoObjeto?.includes(natureza)
    );

    const isFundamentacaoOutro = this.object?.fundamentacaoLegalEntidade?.fundamentacao === 'Outro';
    const is8666 = this.object?.lei === 'LEI_N_8666';

    let rules = {
      numeroProcesso: [
        { rule: 'isMaxLength', maxLength: 4, message: 'Por favor, diminua o tamanho do campo' },
        { rule: 'required', message: 'Por favor, preencha o campo' },
      ],
      dataPedido: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      objeto: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fundamentacao: [
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        isFundamentacaoOutro && !is8666 && { rule: 'required', message: 'Por favor, preencha o campo' },
      ],
      observacoes: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      legislacaoOutros: [{ rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      naturezasDoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      lei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      anoInexigibilidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoObra: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      categoria: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      coordenadas: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    rules = this.isProcessoMigrado()
      ? this.mergeRules(rules, {
          valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          responsavelRatificacao: [
            { rule: 'required', message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
          ],
        })
      : this.mergeRules(rules, {
          numeroSei: [
            { rule: 'required', maxLength: 255, message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
          ],
          responsavelInexigibilidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });

    !this.isLegislacaoAntiga() &&
      (rules = this.mergeRules(rules, {
        termoReferencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      }));

    this.object.lei === 'OUTRA' &&
      (rules = this.mergeRules(rules, {
        legislacaoOutros: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      }));

    !this.isProcessoMigrado() &&
      this.mergeRules(rules, {
        fundamentacaoLegalEntidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        participacaoExclusiva: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });

    return rules;
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const required = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );
    return campos.length === 0;
  }

  validaFornecedoresItens() {
    let validFase = this.object.fornecedores?.length > 0;
    this.object.fornecedores?.forEach((fornecedor) => {
      fornecedor?.itensInexigibilidade?.forEach((item) => {
        if (!item.preenchido) {
          validFase = false;
          return;
        }
      });
    });

    return validFase;
  }

  initializeObra() {
    const localizacao = this.object?.obra?.edificacao?.localizacao;
    const coordenadas = [];
    this.object.categoria = this.object?.obra?.categoria;
    this.object.tipoObra = this.object?.obra?.tipo;

    if (localizacao) {
      switch (localizacao.type) {
        case 'Polygon':
          localizacao.coordinates[0].forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        case 'LineString':
          localizacao.coordinates.forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        default:
          coordenadas.push(localizacao.coordinates.join(' '));
      }
    }

    this.object.coordenadas = coordenadas.join(', ');
    this.object.tipoSelecao = this.getTipoSelecaoByTipoEdificacao(localizacao?.type);
  }

  @action
  carregarEdificacaoObra() {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: this.object?.obra?.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const edificacao = response.data?.items[0];
          this.object.obra.edificacao = edificacao;
          this.edificacao = edificacao;
          this.initializeObra();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  getTipoSelecaoByTipoEdificacao(tipo) {
    return { Point: 'PONTO', LineString: 'LINHA', Polygon: 'POLIGONO' }[tipo];
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  updateObraDTOAtt(att, value) {
    this.obraObject[att] = value;
  }

  @action
  updateObraAttribute(att, value) {
    this.object.obra[att] = value;
  }

  @action
  updateStateObraAttribute(att, value) {
    this.stateObra[att] = value;
  }

  @action
  resetStateObra() {
    this.stateObra = {};
  }

  @action
  initializeObraDTO() {
    this.obraObject = {};
    this.edificacao = undefined;
  }

  setEntidadeContexto(entidadeId) {
    if (this.object && entidadeId) {
      this.entidadeSelectStore.initialize(
        entidadeId,
        () => (this.object.entidade = this.entidadeSelectStore.selectedItem)
      );
    }
  }

  clearFundamentacaoTexto() {
    if (this.object) {
      this.object.fundamentacao = null;
    }
  }

  @action
  setInexigibilidade(inexigibilidade, callback) {
    this.object = inexigibilidade;
    callback && callback();
  }

  @action
  getEntidadesFiltradas(event, callback) {
    const query = event.query.trim();

    const queryParams = {
      andParameters: [
        {
          id: '',
          field: 'nome',
          operator: 'CONTAINS',
          value: query,
          formatted: '',
        },
      ],
    };

    EntidadeService.advancedSearch(queryParams)
      .then((response) =>
        runInAction(() => {
          this.entidadesFiltradas = response.data?.items;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  @action
  hasAlert(idInexigibilidade) {
    idInexigibilidade &&
      this.service
        .getAlertaInexigibilidade(idInexigibilidade)
        .then((alerta) => {
          runInAction(() => {
            if (alerta?.data) {
              this.idAlerta = alerta.data;
            }
          });
        })
        .catch(() => {
          runInAction(() => {
            showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
          });
        });
  }
}

export default InexigibilidadeFormStore;
