import { observable, action, runInAction } from 'mobx';
import Dispensa from '~/domains/Dispensa';
import DispensaService from '~/services/DispensaService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import FormBase from 'fc/stores/FormBase';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import FonteRecursoService from '~/services/FonteRecursoService';
import LicitanteIndexStore from '../licitante/indexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TermoIndexStore from '../licitacao/termoIndex';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import FundamentacaoLegalService from '~/services/FundamentacaoLegalService';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import TdaDispensaService from '~/services/TdaDispensaService';
import AccessPermission from '~/constants/AccessPermission';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import FonteRecurso from '~/domains/FonteRecurso';
import VencedoresFormStore from '../vencedores/formStore';
import FundamentacaoLegal from '~/domains/FundamentacaoLegal';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import ObraTipo from '~/domains/ObraTipo';
import ObraCategoria from '~/domains/ObraCategoria';
import ObraTipoService from '~/services/ObraTipoService';
import ObraCategoriaService from '~/services/ObraCategoriaService';
import EdificacaoService from '~/services/EdificacaoService';

class DispensaFormStore extends FormBase {
  @observable fileStore;
  @observable arquivoDispensaList = [];
  @observable idDispensa;
  @observable vencedoresStore;
  @observable enableReqMod = false;
  @observable entidadesFiltradas;
  @observable arquivosTdaDispensa = [];
  @observable fontesRecursos = [];
  @observable fundamentacoesLegais = [];
  @observable loadingTabDadosBasicos = false;
  @observable loadingFundamentacoes = false;
  @observable responsaveisDispensa;
  @observable anos = [];
  @observable obraObject = {};
  @observable edificacao;
  @observable stateObra = {};
  @observable idAlerta = null;
  @observable entidadeDestino = '';
  @observable dataTransferencia = '';
  @observable transferido = false;

  constructor() {
    super(DispensaService, Dispensa);
    this.orgaosParticipantesStore = new AsyncMultiselectStore(Entidade, EntidadeService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
      andParameters: [{ field: 'id', operator: 'NOT_EQUAL_TO', value: AppStore.getContextEntity()?.id }],
    });
    this.fontesRecursoStore = new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.fundamentacaoLegalEntidadeSelectStore = new AsyncDropDownStore(
      FundamentacaoLegal,
      FundamentacaoLegalService,
      'fundamentacao',
      'id'
    );
    this.fornecedoresIndexStore = new LicitanteIndexStore();
    this.termoIndexStore = new TermoIndexStore();
    this.obraTipoStore = new AsyncDropDownStore(ObraTipo, ObraTipoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.obraCategoriaStore = new AsyncDropDownStore(ObraCategoria, ObraCategoriaService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.fileStore = new MultipleFileUploaderStore(
      null,
      [],
      (file) => DispensaService.upload(file),
      (fileDTO, countDownload, onDownload) => DispensaService.download(fileDTO, countDownload, onDownload),
      (idArquivo) => this.removerArquivoDispensa(idArquivo),
      (idArquivo, arquivoDispensaDTO) => this.atualizarArquivoDispensa(idArquivo, arquivoDispensaDTO)
    );

    this.fileStoreTda = new MultipleFileUploaderStore(
      null,
      [],
      (file) => TdaDispensaService.upload(file),
      (fileDTO) => TdaDispensaService.download(fileDTO)
    );

    this.dispensaService = DispensaService;

    this.criarDispensa = this.criarDispensa.bind(this);
    this.removerArquivoDispensa = this.removerArquivoDispensa.bind(this);
    this.atualizarArquivoDispensa = this.atualizarArquivoDispensa.bind(this);
    this.getEntidadesFiltradas = this.getEntidadesFiltradas.bind(this);
    this.setDispensa = this.setDispensa.bind(this);

    this.loadTipos();
    this.vencedoresStore = new VencedoresFormStore();
  }

  @action
  initializeArquivos(idDispensa, callback) {
    if (idDispensa) {
      const promisses = [];
      promisses.push(this.service.getById(idDispensa));
      promisses.push(this.service.recuperarArquivos(idDispensa));

      Promise.all(promisses)
        .then((response) => {
          const arquivosRecuperados = response[1].data;
          this.fileStore.initialize(arquivosRecuperados);
          this.arquivoDispensaList = arquivosRecuperados;
          this.dispensa = response[0].data;

          callback && callback();
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivosDispensa: this.arquivoDispensaList, lei: this.object.lei })
      .then(() => runInAction(() => callback && callback()))
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  isLegislacaoAntiga() {
    return this.object.lei === 'LEI_N_8666';
  }

  isProcessoMigrado() {
    return this.object.processoMigrado === true;
  }

  fundamentacaoLegalFiltrada(lei) {
    return {
      page: { index: 1, size: 100 },
      sort: {
        by: 'fundamentacao',
        order: 'asc',
      },
      andParameters: [
        { field: 'ativo', operator: SearchOperators.EQUAL_TO.value, value: true },
        { field: 'tipoDispensa', operator: SearchOperators.EQUAL_TO.value, value: true },
        { field: 'legislacao', operator: SearchOperators.EQUAL_TO.value, value: lei },
      ],
    };
  }

  @action
  initializeTabDadosBasicos(callback) {
    const legislacao = this.object.lei ?? 'LEI_N_14133';
    const fundamentacaoFiltrada = this.fundamentacaoLegalFiltrada(legislacao);
    this.loadingTabDadosBasicos = true;
    const filtro = {
      tipoProcesso: 'DISPENSA',
      filtros: this.object?.naturezasDoObjeto?.includes('OBRAS') ? [legislacao, 'NATUREZAOBJ_OBRA'] : [legislacao],
    };
    Promise.all([
      FonteRecursoService.getAll(),
      ObrigatoriedadeArquivoService.getArquivosObrigatorios(filtro),
      FundamentacaoLegalService.advancedSearch(fundamentacaoFiltrada),
      DispensaService.getAnosDispensa(),
    ])
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response[0].data;
          const tiposArquivos = response[1]?.data ?? [];
          this.fileStore.tipoArquivoEnum = tiposArquivos.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoDispensa().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
          this.fundamentacoesLegais = response[2].data.items;
          this.anos = response[3].data.map((ano) => ({ text: ano.toString(), value: ano }));
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loadingTabDadosBasicos = false;
        callback && callback();
      });
  }

  @action
  carregarFundamentacaoLegal(lei, callback) {
    this.loadingFundamentacoes = true;
    FundamentacaoLegalService.advancedSearch(this.fundamentacaoLegalFiltrada(lei))
      .then((response) =>
        runInAction(() => {
          this.fundamentacoesLegais = response.data.items;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loadingFundamentacoes = false;
        })
      );
  }

  @action
  loadTipos(lei, callback) {
    let legislacao;
    let naturezas;
    if (lei) {
      legislacao = lei;
    } else {
      legislacao = this.dispensa?.processoMigrado
        ? 'LEI_N_8666'
        : this.dispensa?.lei
        ? this.dispensa?.lei
        : 'LEI_N_14133';
    }
    if (!this.object?.naturezasDoObjeto) {
      naturezas = this.dispensa?.naturezasDoObjeto.includes('OBRAS');
    } else {
      naturezas = this.object?.naturezasDoObjeto.includes('OBRAS');
    }
    const filtro = {
      tipoProcesso: 'DISPENSA',
      filtros: naturezas ? [legislacao, 'NATUREZAOBJ_OBRA'] : [legislacao],
    };

    ObrigatoriedadeArquivoService.getArquivosObrigatorios(filtro)
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoDispensa().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });

          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  _onCreate() {
    const { coordenadas, coordenadasObraDTO, tipoCamadaObraDTO } = this.stateObra;
    if (coordenadas) {
      this.updateObraDTOAtt('tipoCamada', tipoCamadaObraDTO);
      this.updateObraDTOAtt('coordenadas', coordenadasObraDTO);
      this.updateAttribute('coordenadas', coordenadas);
    }
  }

  getFilterSuggestTermo() {
    const filterSuggest = [
      {
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
      {
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    return filterSuggest;
  }

  criarDispensa(dispensaDTO, callback) {
    this.loading = true;
    this.dispensaService
      .criarDispensa(dispensaDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          const notificationMessage = dispensaDTO.id ? 'Dispensa editada com sucesso!' : 'Dispensa criada com sucesso!';
          showNotification('success', null, notificationMessage);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  salvarTransferenciaEntidade(dispensa, callback) {
    const dispensaDTO = {
      id: this.object.id,
      dispensa: {
        ...dispensa,
      },
    };
    this.loading = true;
    this.service
      .salvarTransferenciaEntidade(dispensaDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Transferência de entidade salva com sucesso!');
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  atualizaFornecedores(fornecedores) {
    const removidos = this.object.fornecedores?.filter(
      (forn) => !fornecedores.map((f) => f.id).includes(forn.licitante.id)
    );
    this.object.licitantes = fornecedores;
    removidos?.forEach((f) => this.removeFornecedor(f.licitante));
  }

  checkFornecedorIsVencedor(fornecedor) {
    return this.object.fornecedores?.some((f) => f.licitante.id == fornecedor.id);
  }

  @action
  removeFornecedor(fornecedor) {
    this.object.licitantes = this.object.licitantes.filter((l) => l.id !== fornecedor.id);
    this.object.fornecedores = this.object.fornecedores?.filter((f) => f.licitante.id !== fornecedor.id);
    this.updateLicitantesFornecedoresVencedoresStore();
  }

  @action
  updateLicitantesFornecedoresVencedoresStore() {
    this.vencedoresStore.licitantes = this.object.licitantes;
    this.vencedoresStore.vencedores = this.object.fornecedores;
  }

  @action
  initializeVencedorStore() {
    const idsUnicos = new Set();
    const licitantes = this.object.fornecedores
      .map((f) => f.licitante)
      .filter((licitante) => {
        if (idsUnicos.has(licitante.id)) {
          return false;
        } else {
          idsUnicos.add(licitante.id);
          return true;
        }
      });

    this.object.licitantes = licitantes;

    this.vencedoresStore.initialize(
      this.object.termoReferencia,
      this.object.licitantes ?? [],
      this.object.fornecedores ?? [],
      this.object.lotesFracassados?.length ? this.object.lotesFracassados : [],
      this.object.itensDesertos?.length ? this.object.itensDesertos : [],
      this.object.tipoAdjudicacao,
      (fornecedores) => {
        this.updateAttribute('fornecedores', fornecedores);
        !this.object.processoMigrado && this.setValor(this.vencedoresStore.getValorTotal(this.isLegislacaoAntiga()));
      },
      (fracassado) => this.updateAttribute('lotesFracassados', fracassado),
      (desertos) => this.updateAttribute('itensDesertos', desertos),
      (tipoAdjudicacao) => {
        this.updateAttribute('tipoAdjudicacao', tipoAdjudicacao);
      },
      this.object.processoMigrado
    );

    !this.object.valor && this.setValor(this.vencedoresStore.getValorTotal(this.isLegislacaoAntiga()));
  }

  @action
  setValor(valor) {
    this.object.valor = valor;
  }

  removerArquivoDispensa(idArquivo) {
    return DispensaService.removerArquivo(this.idDispensa, idArquivo);
  }

  atualizarArquivoDispensa(idArquivo, arquivoDispensaDTO) {
    return DispensaService.atualizarArquivo(this.idDispensa, idArquivo, arquivoDispensaDTO, this.object.lei);
  }

  @action
  setArquivoDispensaList(arquivoDispensaList) {
    this.arquivoDispensaList = arquivoDispensaList;
  }

  @action
  setUploadedFiles(arquivos) {
    this.fileStore.uploadedFiles = arquivos.map((arquivo) => {
      const diretorioDividido = arquivo.diretorio.split('\\');
      const nomeArquivo = diretorioDividido[diretorioDividido.length - 1].split('.');
      arquivo.arquivo = {
        lookupId: nomeArquivo[0],
        nomeOriginal: arquivo.nome,
        tipoArquivo: `application/${nomeArquivo[1]}`,
      };
      return arquivo;
    });
  }

  rulesDefinition() {
    const isObra = ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].some((natureza) =>
      this.object?.naturezasDoObjeto?.includes(natureza)
    );

    const isFundamentacaoOutro = this.object?.fundamentacaoLegalEntidade?.fundamentacao === 'Outro';
    const is8666 = this.object?.lei === 'LEI_N_8666';

    let rules = {
      naturezasDoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      numeroProcesso: [
        { rule: 'isMaxLength', maxLength: 4, message: 'Por favor, diminua o tamanho do campo' },
        { rule: 'required', message: 'Por favor, preencha o campo' },
      ],
      dataPedido: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      objeto: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fundamentacao: [
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        isFundamentacaoOutro && !is8666 && { rule: 'required', message: 'Por favor, preencha o campo' },
      ],
      observacoes: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      legislacaoOutros: [{ rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      lei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      anoDispensa: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoObra: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      categoria: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      coordenadas: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    rules = this.isProcessoMigrado()
      ? this.mergeRules(rules, {
          valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          gestor: [
            { rule: 'required', message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
          ],
        })
      : this.mergeRules(rules, {
          fundamentacaoLegalEntidade: [
            { rule: 'required', message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
          ],
          numeroProcessoSEI: [
            { rule: 'required', maxLength: 255, message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
          ],
          responsavelDispensa: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          participacaoExclusiva: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });

    !this.isLegislacaoAntiga() &&
      (rules = this.mergeRules(rules, {
        termoReferencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      }));

    this.object.lei === 'OUTRA' &&
      (rules = this.mergeRules(rules, {
        legislacaoOutros: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      }));

    return rules;
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const dadosBasicos = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosBasicos.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );
    return campos.length === 0;
  }

  validaFornecedoresItens() {
    let validFase = this.object.fornecedores?.length > 0;
    this.object.fornecedores?.forEach((fornecedor) => {
      fornecedor?.itensDispensa?.forEach((item) => {
        if (!item.preenchido) {
          validFase = false;
          return;
        }
      });
    });

    return validFase;
  }

  checkDataCadastro() {
    let data = this.object?.dataCadastro;
    if (data) {
      const current = moment();
      const diff = moment.duration(current.diff(data));

      this.enableReqMod = diff.asHours() > 24;
    }
  }

  initializeObra() {
    const localizacao = this.object?.obra?.edificacao?.localizacao;
    const coordenadas = [];
    this.object.categoria = this.object?.obra?.categoria;
    this.object.tipoObra = this.object?.obra?.tipo;

    if (localizacao) {
      switch (localizacao.type) {
        case 'Polygon':
          localizacao.coordinates[0].forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        case 'LineString':
          localizacao.coordinates.forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        default:
          coordenadas.push(localizacao.coordinates.join(' '));
      }
    }

    this.object.coordenadas = coordenadas.join(', ');
    this.object.tipoSelecao = this.getTipoSelecaoByTipoEdificacao(localizacao?.type);
  }

  @action
  carregarEdificacaoObra() {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: this.object?.obra?.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const edificacao = response.data?.items[0];
          this.object.obra.edificacao = edificacao;
          this.edificacao = edificacao;
          this.initializeObra();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  getTipoSelecaoByTipoEdificacao(tipo) {
    return { Point: 'PONTO', LineString: 'LINHA', Polygon: 'POLIGONO' }[tipo];
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  updateObraDTOAtt(att, value) {
    this.obraObject[att] = value;
  }

  @action
  updateObraAttribute(att, value) {
    this.object.obra[att] = value;
  }

  @action
  updateStateObraAttribute(att, value) {
    this.stateObra[att] = value;
  }

  @action
  resetStateObra() {
    this.stateObra = {};
  }

  @action
  initializeObraDTO() {
    this.obraObject = {};
    this.edificacao = undefined;
  }

  @action
  setDispensa(dispensa, callback) {
    this.object = dispensa;
    callback && callback();
  }

  initializeTdaDispensa(idDispensa) {
    if (idDispensa && this.hasPermissionTda()) {
      TdaDispensaService.tdaDispensaByIdDispensa(idDispensa)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaDispensa = response.data;
              this.carregaArquivosTda(tdaDispensa);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  carregaArquivosTda(tdaDispensa) {
    if (tdaDispensa) {
      TdaDispensaService.recuperarArquivos(tdaDispensa.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaDispensa.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.arquivosTdaDispensa = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  @action
  hasAlert(idDispensa) {
    idDispensa &&
      this.service
        .getAlertaDispensa(idDispensa)
        .then((alerta) => {
          runInAction(() => {
            if (alerta?.data) {
              this.idAlerta = alerta.data;
            }
          });
        })
        .catch(() => {
          runInAction(() => {
            showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
          });
        });
  }

  @action
  getEntidadesFiltradas(event, callback) {
    const query = event.query.trim();

    const queryParams = {
      andParameters: [
        {
          id: '',
          field: 'nome',
          operator: 'CONTAINS',
          value: query,
          formatted: '',
        },
      ],
    };

    EntidadeService.advancedSearch(queryParams)
      .then((response) =>
        runInAction(() => {
          this.entidadesFiltradas = response.data?.items;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  @action
  getPessoasResponsaveis(callback) {
    const entidade = AppStore.getContextEntity();
    entidade &&
      this.service
        .getPessoasResponsaveis(entidade?.id)
        .then((response) =>
          runInAction(() => {
            this.responsaveisDispensa = response.data;
            if (
              this.object.idResponsavelDispensa &&
              this.object.responsavelDispensa &&
              !this.responsaveisDispensa.some(
                (pessoa) =>
                  pessoa.id == this.object.idResponsavelDispensa && pessoa.nome == this.object.responsavelDispensa
              )
            ) {
              this.responsaveisDispensa?.push({
                nome: this.object.responsavelDispensa,
                id: this.object.idResponsavelDispensa,
              });
            }
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
  }

  @action
  updateResponsavelDispensaAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.object[attribute] = value;
    const nomePessoaResponsavel = this.responsaveisDispensa.find((pessoa) => pessoa.id == value)?.nome;
    this.object['responsavelDispensa'] = nomePessoaResponsavel;
  }
}

export default DispensaFormStore;
