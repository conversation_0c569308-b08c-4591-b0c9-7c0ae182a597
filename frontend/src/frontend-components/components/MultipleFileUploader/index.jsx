import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import React from 'react';
import { FileUpload } from 'primereact/fileupload';
import FileUploadStore from '../../stores/MultipleFileUploaderStore';
import { Dropdown } from 'primereact/dropdown';
import './style.scss';
import { ConfirmDialog } from 'primereact/confirmdialog';
import moment from 'moment';
import 'moment/locale/pt-br';
import Fc<PERSON>utton from '../FcButton';
import { Dialog } from 'primereact/dialog';
import HelpIcon from './HelpIcon';
import { DataView, DataViewLayoutOptions } from 'primereact/dataview';
import { Card } from 'primereact/card';
import {
  formatBytes,
  getLightenColor,
  getNumberUnitThousands,
  getOriginUrl,
  getValueByKey,
  isValueValid,
  showNotification,
  checkUserGroup,
} from '../../utils/utils';
import { InputTextarea } from 'primereact/inputtextarea';
import { classNames } from 'primereact/utils';
import { Image } from 'primereact/image';
import { screenBreakpoints } from '../../constants/screenBreakpoints';
import { Tag } from 'primereact/tag';
import Tooltip from '../Tooltip';

@observer
class MultipleFileUploader extends React.Component {
  constructor(props) {
    super(props);
    this.ref = React.createRef();
    this.store = props.store;

    this.state = {
      visibleDialogRemove: false,
      rowDataRemove: null,
      viewFileUrl: undefined,
      showVisualizationDialog: false,
      arquivos: [],
      showDialogDescription: false,
      confirmationDialog: {
        show: false,
        onConfirm: () => {},
        content: '',
      },
      selectedType: null,
      descricaoArq: '',
      currentFile: null,
      descricoesArquivosMap: {},
      selectedTypes: {},
      windowWidth: window.innerWidth,
      originalOrder: this._storeOriginalOrder(this._getFilteredListEnumArquivos()),
      currentPage: 0,
    };

    this._renderButtons = this._renderButtons.bind(this);
    this._onUploadFile = this._onUploadFile.bind(this);
    this._renderGridItem = this._renderGridItem.bind(this);
    this._itemTemplate = this._itemTemplate.bind(this);
    this._renderListItem = this._renderListItem.bind(this);
    this._renderHeader = this._renderHeader.bind(this);
    this._renderCardItem = this._renderCardItem.bind(this);
    this._handleResize = this._handleResize.bind(this);
    this._onPageChange = this._onPageChange.bind(this);
    this._renderRequiredFilesIcon = this._renderRequiredFilesIcon.bind(this);
  }

  componentDidMount() {
    this.store.removeFilesState = () => this.setState({ arquivos: [] });
    this.store.setFilesState = (files) => this.setArquivos(files);
    const { files } = this.props;

    files && this.store.initialize(this.props.files);
    this.setArquivos(this.store.keyedUploadedFiles);
    if (this.state.windowWidth <= screenBreakpoints.sm) this.store.setlayout('list');
    window.addEventListener('resize', this._handleResize);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this._handleResize);
  }

  _handleResize = () => {
    this.setState({
      windowWidth: window.innerWidth,
    });
    if (this.state.windowWidth <= screenBreakpoints.sm) this.store.setlayout('list');
  };

  setArquivos(arquivos) {
    const filesToUpdateKey = [];
    const uploadFiles = arquivos.map((file, idx) => {
      const keyValue = moment().toISOString() + idx;
      const keydFile = { ...file, key: keyValue };
      filesToUpdateKey.push(keydFile);
      const arquivo = {
        ...file,
        key: keyValue,
        arquivo: file.arquivo?.nomeOriginal,
        dataEnvio: file.dataEnvio,
        descricao: file.descricao,
        tipo: file.tipo,
        tamanho: file.size,
        fase: this.store.fase,
      };

      if (file?.fase) arquivo.fase = file?.fase;

      return arquivo;
    });
    this.store.updateUploadedFilesKeys(filesToUpdateKey);
    const selectedTypes = {};
    uploadFiles.forEach((file) => {
      selectedTypes[file.key] = file.tipo;
    });
    this.setState({ arquivos: uploadFiles, selectedTypes });
  }

  _findByKey(list, key) {
    return list.find((element) => element.key === key);
  }

  _onUploadFile(arquivo, callbackSuccess, callbackFail) {
    const { isObra } = this.props;

    const callback = (fileList) => {
      this._onChangeFiles(fileList);
      callbackSuccess();
    };

    const fileData = {
      arquivo: arquivo.file,
      descricao: arquivo.descricao,
      tipoArquivo: arquivo.tipo,
      key: arquivo.key,
      ...(isObra && { fase: arquivo.fase }),
      fase: this.store.fase,
    };

    this.store.uploadFile(fileData, callback, callbackFail);
    this.forceUpdate();
  }

  _onPageChange(event) {
    this.setState({ currentPage: event.first / event.rows });
  }

  getCardTags(fileData, tags) {
    return tags?.map((fieldItem, index) => {
      let listKeyValue = fileData[fieldItem.field];
      fieldItem.body && (listKeyValue = fieldItem.body(fileData));
      return (
        <Tooltip key={index} value={fieldItem.tooltip} sideOffset={0}>
          <div style={{ cursor: 'default' }}>
            <Tag
              icon={fieldItem.icon}
              style={{
                color: fieldItem.color,
                backgroundColor: getLightenColor(fieldItem.color, 0.7),
                border: `1px solid ${fieldItem.color}`,
              }}
            >
              <span> {listKeyValue}</span>
            </Tag>
          </div>
        </Tooltip>
      );
    });
  }

  _getFilteredList() {
    const { filterTypes } = this.props;

    return filterTypes
      ? this.state.arquivos.filter(
          (f) =>
            ((!filterTypes.included || filterTypes.included.includes(f.tipo)) &&
              (!filterTypes.excluded || !filterTypes.excluded.includes(f.tipo)) &&
              (!f.fase || !filterTypes.filter || filterTypes.filter.values.includes(f[filterTypes.filter.column]))) ||
            !f.tipo
        )
      : this.state.arquivos;
  }

  _getFilteredListEnumArquivos() {
    const { filterTypes, isObra } = this.props;
    const { tipoArquivoEnum } = this.store;

    return filterTypes && isObra
      ? tipoArquivoEnum.filter(
          (f) =>
            ((!filterTypes.included || filterTypes.included.includes(f.arquivoEnum)) &&
              (!filterTypes.excluded || !filterTypes.excluded.includes(f.arquivoEnum)) &&
              (!f.fase || !filterTypes.filter || filterTypes.filter.values.includes(f[filterTypes.filter.column]))) ||
            !f.arquivoEnum
        )
      : tipoArquivoEnum;
  }

  hasAllRequiredFiles() {
    const requiredFileTypes = this._getFilteredListEnumArquivos()
      .filter((arquivo) => arquivo.obrigatorio)
      .map((arquivo) => ({ value: arquivo.value, fase: arquivo.fase }));

    if (requiredFileTypes.length === 0) {
      return null;
    }

    const uploadedFileTypes = this.store.uploadedFiles.map((arquivo) => ({
      tipo: arquivo.tipo,
      fase: arquivo.fase,
    }));

    return requiredFileTypes.every((requiredFile) =>
      uploadedFileTypes.some(
        (uploadedFile) =>
          requiredFile.value === uploadedFile.tipo && (!requiredFile.fase || requiredFile.fase === uploadedFile.fase)
      )
    );
  }

  _onChangeFiles(fileList) {
    const { onChangeFiles, isObra, faseObra } = this.props;
    isObra
      ? onChangeFiles(
          fileList?.map((file) => {
            if (!file.fase) {
              file.fase = faseObra;
            }
            return file;
          })
        )
      : onChangeFiles(fileList);
  }
  _renderRemoveFileDialog() {
    const message = `${
      this.state.rowDataRemove.idArquivo ? 'Este arquivo está persistido e será removido permanentemente. ' : ''
    }Você realmente deseja remover este arquivo?`;

    let arquivos = [...this.state.arquivos];
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, this.state.rowDataRemove?.key);
    arquivos = arquivos.filter((arquivo) => arquivo.key !== this.state.rowDataRemove?.key);

    return (
      <ConfirmDialog
        visible={this.state.visibleDialogRemove}
        message={message}
        header="Excluir Arquivo"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.removeFile(arquivo, (fileList) => this._onChangeFiles(fileList));
          const updatedSelectedTypes = { ...this.state.selectedTypes };
          delete updatedSelectedTypes[this.state.rowDataRemove?.key];

          this.setState({
            visibleDialogRemove: false,
            rowDataRemove: null,
            arquivos,
            selectedTypes: updatedSelectedTypes,
          });
          this.props.ShowSaveRemoveNotifications && showNotification('info', null, 'Arquivo removido com sucesso!');
          this.forceUpdate();
        }}
        onHide={() => this.setState({ visibleDialogRemove: false, rowDataRemove: null })}
      />
    );
  }

  _renderDescriptionDialog() {
    const fileTypeOptions = this.props.fileTypeOptions[this.state.selectedTypes[this.state.currentFile.key]];
    const placeholder = fileTypeOptions?.placeholder ?? 'Informe a descrição';
    const fileData = this.state.currentFile;
    const currentDescription = this.state.descricoesArquivosMap[fileData.key] ?? '';
    return (
      <Dialog
        header="Descrição"
        className="sm:w-26rem lg:w-17rem xl:w-30rem"
        visible={this.state.showDialogDescription}
        onHide={() => this.setState({ showDialogDescription: false })}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => this.setState({ showDialogDescription: false })}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              disabled={!currentDescription.trim()}
              onClick={() => {
                this.setState({
                  descricoesArquivosMap: { ...this.state.descricoesArquivosMap, [fileData.key]: currentDescription },
                  showDialogDescription: false,
                });
                this.store.updateFiles(
                  fileData.key,
                  'descricao',
                  currentDescription,
                  this._findByKey(this.state.arquivos, fileData.key),
                  (fileList) => this._onChangeFiles(fileList)
                );
              }}
            />
          </div>
        }
      >
        <InputTextarea
          type="text"
          value={currentDescription}
          rows={5}
          cols={30}
          style={{ width: '100%' }}
          placeholder={placeholder}
          maxLength={255}
          disabled={this.props.downloadOnly}
          onChange={(e) =>
            this.setState((prevState) => ({
              descricoesArquivosMap: {
                ...prevState.descricoesArquivosMap,
                [fileData.key]: e.target.value,
              },
            }))
          }
          onBlur={() => {
            !currentDescription.trim() &&
              this.setState((prevState) => ({
                descricoesArquivosMap: {
                  ...prevState.descricoesArquivosMap,
                  [fileData.key]: '',
                },
              }));
          }}
        />
        <p>
          {currentDescription
            ? `${getNumberUnitThousands(255 - currentDescription.length)} `
            : `${getNumberUnitThousands(255)} `}
          Caracteres Restantes
        </p>
      </Dialog>
    );
  }

  _renderConfirmationDialog() {
    return (
      <Dialog
        header="Confirmação"
        className="sm:w-26rem lg:w-17rem xl:w-30rem"
        visible={this.state.confirmationDialog.show}
        onHide={() => this.setState({ confirmationDialog: { show: false } })}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => this.setState({ confirmationDialog: { show: false } })}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                this.state.confirmationDialog.onConfirm();
                this.setState({ confirmationDialog: { show: false } });
              }}
            />
          </div>
        }
      >
        <p>{this.state.confirmationDialog.content}</p>
      </Dialog>
    );
  }

  _renderButtons(rowData) {
    const { downloadOnly, countDownloadRequest } = this.props;

    if (downloadOnly) {
      return (
        <div className="flex justify-content-end">
          <FcButton
            type="button"
            className="p-button-text toggle-button"
            tooltipOptions={{ position: 'top' }}
            tooltip="Download"
            icon="pi pi-download"
            onClick={() => {
              const arquivo = this._findByKey(this.store.keyedUploadedFiles, rowData.key);
              this.store.downloadFile(arquivo.arquivo, countDownloadRequest);
            }}
          />
        </div>
      );
    } else {
      return (
        <div className="flex justify-content-end">
          {
            <FcButton
              type="button"
              tooltip="Descrição"
              tooltipOptions={{ position: 'top' }}
              icon="pi pi-comment"
              className="p-button-info p-button-rounded p-button-text"
              onClick={() => {
                this.setState({
                  showDialogDescription: true,
                  currentFile: rowData,
                  descricaoArq: this.state.descricoesArquivosMap[rowData.key] ?? rowData.descricao ?? '',
                });
              }}
            />
          }
          <FcButton
            type="button"
            className="p-button-text toggle-button"
            tooltip="Download"
            icon="pi pi-download"
            tooltipOptions={{ position: 'top' }}
            onClick={() => {
              const arquivo = this._findByKey(this.store.keyedUploadedFiles, rowData.key);
              this.store.downloadFile(arquivo.arquivo, countDownloadRequest);
            }}
          />
          <FcButton
            type="button"
            tooltip="Remover"
            icon="pi pi-trash"
            tooltipOptions={{ position: 'top' }}
            className="p-button-rounded p-button-text p-button-danger"
            onClick={() => this.setState({ visibleDialogRemove: true, rowDataRemove: rowData })}
          />
        </div>
      );
    }
  }

  viewDoc(fileData) {
    const { countDownloadRequest } = this.props;
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, fileData.key);
    this.store.downloadFile(arquivo.arquivo, countDownloadRequest, (viewFileUrl) =>
      this.setState({ showVisualizationDialog: true, viewFileUrl })
    );
  }

  checkTypeDoc(fileData) {
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, fileData.key);
    const nomeOriginal = arquivo?.arquivo?.nomeOriginal.toLowerCase();
    return (
      nomeOriginal?.endsWith('.pdf') ||
      nomeOriginal?.endsWith('.png') ||
      nomeOriginal?.endsWith('.jpeg') ||
      nomeOriginal?.endsWith('.jpg') ||
      nomeOriginal?.endsWith('.kml')
    );
  }

  _renderFileImage(imageUrl, fileData, className = 'desktop-image') {
    return (
      <Image
        className={className}
        imageClassName="cursor-pointer"
        imageStyle={{
          paddingTop: className === 'desktop-image' ? '0.5rem' : '0',
          height: className === 'desktop-image' ? '6rem' : '3rem',
          padding: className === 'desktop-image' ? '0.5rem' : '0',
        }}
        src={imageUrl}
        onClick={() => {
          this.checkTypeDoc(fileData) && this.viewDoc(fileData);
        }}
      />
    );
  }

  _renderCardItem(fileData, columnSize) {
    const { showFileType, downloadOnly, tags } = this.props;
    const originUrl = getOriginUrl();
    const fileName = fileData.arquivo;
    const fileType = this._getFileType(fileName?.toLowerCase?.());
    const imageUrl = this._getFileTypeImage(originUrl, fileType);
    const valueType =
      this._findByKey(this.store.uploadedFiles, fileData.key)?.tipo ??
      this._findByKey(this.state.arquivos, fileData.key)?.tipo ??
      fileData?.tipo;
    const formattedSize = this._getFormattedSize(showFileType, downloadOnly, fileData);
    const descricao = this.state.descricoesArquivosMap[fileData.key] || fileData.descricao;
    if (downloadOnly) {
      let fileSize = fileData?.tamanho || fileData?.size;
      if (!fileSize && fileData?.file && fileData.file.size) {
        fileSize = fileData.file.size;
      }
      return (
        <div className={`file-card-container card-wrap ${columnSize}`}>
          <Card className="card-grid">
            <div className="flex align-items-center justify-content-between">
              <div className="flex w-full align-items-center justify-content-between">
                {this._renderFileImage(imageUrl, fileData, 'desktop-image')}
                <div className="w-full">
                  <div className="title-wrapper">
                    {this._renderFileImage(imageUrl, fileData, 'mobile-image')}
                    <span
                      className="card-title cursor-pointer"
                      style={{ fontWeight: 'bold', fontSize: '1.15rem', color: '#24292f' }}
                      onClick={() => {
                        this.checkTypeDoc(fileData) && this.viewDoc(fileData);
                      }}
                    >
                      {checkUserGroup('Jurisdicionado') ? 'Tipo do Documento: ' : ''}
                      {getValueByKey(valueType, this.props.fileTypes) || 'Tipo não definido'}
                    </span>
                  </div>
                  {descricao && descricao.trim() && (
                    <div style={{ margin: '0.25rem 0 0.5rem 0' }}>
                      <span className="file-description-text">{descricao}</span>
                    </div>
                  )}
                  <div className="gray-text ml-0" style={{ marginBottom: '0.25rem', fontSize: '0.85rem' }}>
                    {typeof fileSize === 'number' && fileSize > 0 && (
                      <span>
                        {formatBytes(fileSize)} <span style={{ color: '#ccc' }}>|</span>{' '}
                      </span>
                    )}
                    Data de Envio:{' '}
                    {fileData.dataEnvio && moment(fileData.dataEnvio).isValid()
                      ? moment(fileData.dataEnvio).format('DD/MM/YYYY HH:mm')
                      : ''}
                  </div>
                  <div className="gray-text ml-0 file-description-text">{fileName}</div>
                </div>
              </div>
              {tags ? (
                <div>
                  <div className="pt-5 flex justify-content-between">{this._renderButtons(fileData)}</div>
                  <div className="pl-2 flex gap-1 flex-wrap">{this.getCardTags(fileData, tags)}</div>
                </div>
              ) : (
                this._renderButtons(fileData)
              )}
            </div>
          </Card>
        </div>
      );
    }

    return (
      <div className={`file-card-container card-wrap ${columnSize}`}>
        <Card className="card-grid">
          <div className="flex align-items-center justify-content-between">
            <div className="flex w-full align-items-center justify-content-between">
              {this._renderFileImage(imageUrl, fileData, 'desktop-image')}
              <div className="w-full">
                <div className="title-wrapper">
                  {this._renderFileImage(imageUrl, fileData, 'mobile-image')}
                  <span
                    className="card-title cursor-pointer"
                    style={{ fontWeight: 'bold', fontSize: '1.15rem', color: '#24292f' }}
                    onClick={() => {
                      this.checkTypeDoc(fileData) && this.viewDoc(fileData);
                    }}
                  >
                    {checkUserGroup('Jurisdicionado') ? 'Tipo do Arquivo: ' : ''}
                    {getValueByKey(valueType, this.props.fileTypes) || 'Tipo não definido'}
                  </span>
                </div>
                <div className="date-text">{this._renderDate(fileData)}</div>
                <div className="card-cont-desc">
                  {formattedSize}
                  {this._renderDescription(fileData)}
                </div>
                <div className="gray-text ml-0 file-name-text">Nome do Arquivo: {fileData.arquivo}</div>
                {this.props.fileTypes.length > 0 && (
                  <div className="flex card-cont-tipo ">
                    {this._renderDocumentTypeDropdown(showFileType, downloadOnly, valueType, fileData)}
                  </div>
                )}
              </div>
            </div>
            {tags ? (
              <div className="pt-5 flex justify-content-between">{this._renderButtons(fileData)}</div>
            ) : (
              this._renderButtons(fileData)
            )}
          </div>
          {tags && <div className="pl-2 flex gap-1 flex-wrap">{this.getCardTags(fileData, tags)}</div>}
        </Card>
      </div>
    );
  }

  _handleTypeChange = (fileData, e) => {
    const content = this.props.fileTypeOptions[e.value]?.textConfirmationDialog;
    if (content) {
      this.setState({
        confirmationDialog: {
          show: true,
          onConfirm: () => this.onSelectDocumentType(fileData, e),
          content,
        },
      });
    } else {
      this.onSelectDocumentType(fileData, e);
    }
  };

  onSelectDocumentType(fileData, e) {
    const { value } = e;
    const { selectedTypes } = this.state;

    const newSelectedTypes = { ...selectedTypes, [fileData.key]: value };

    this.setState(
      {
        selectedTypes: newSelectedTypes,
      },
      () => {
        this.store.updateFiles(
          fileData.key,
          'tipo',
          value,
          this._findByKey(this.state.arquivos, fileData.key),
          (fileList) => {
            this._onChangeFiles(fileList);
            this.forceUpdate();
          }
        );
      }
    );
  }

  _storeOriginalOrder(files) {
    return files.reduce((acc, file, index) => {
      acc[file.value] = index;
      return acc;
    }, {});
  }

  _renderDocumentTypeDropdown(showFileType, downloadOnly, valueType, fileData) {
    const { selectedTypes } = this.state;

    const options = this._sortRequiredFiles(this._getFilteredListEnumArquivos(), selectedTypes).map((option) => ({
      ...option,
      className: classNames({
        'selected-option': Object.values(selectedTypes).includes(option.value),
      }),
    }));

    if (!downloadOnly && showFileType) {
      return (
        <>
          {checkUserGroup('Jurisdicionado') && <div className="space-type">Tipo do Documento:</div>}
          <div className="dropdown-bord">
            <Dropdown
              value={selectedTypes[fileData.key] || valueType}
              className="sm:w-5rem lg:w-10rem xl:w-15rem dropdown-a"
              options={options}
              optionLabel="text"
              optionValue="value"
              onChange={(e) => this._handleTypeChange(fileData, e)}
              placeholder="Selecione um tipo"
            />
          </div>
        </>
      );
    } else {
      return (
        <div>
          <>
            {checkUserGroup('Jurisdicionado') && <b>Tipo do Documento:</b>}{' '}
            {getValueByKey(
              this._findByKey(this.state.arquivos, fileData?.key)?.tipo ?? fileData.tipo,
              this.props.fileTypes
            )}
          </>
          <div className="file-name-text">Nome do Arquivo: {fileData?.arquivo}</div>
        </div>
      );
    }
  }

  _renderDate({ dataEnvio }) {
    if (!dataEnvio || !moment(dataEnvio).isValid()) {
      return null;
    }

    const formattedDate = moment(dataEnvio).format('DD/MM/YYYY HH:mm');
    return <div className="gray-text text-overflow ml-0">{`Data de Envio: ${formattedDate}`}</div>;
  }

  _renderDescription(fileData) {
    const descricao = this.state.descricoesArquivosMap[fileData.key] || fileData.descricao;
    const isOutrosDocumentos = this.state.selectedTypes[fileData.key] === 'OUTROS_DOCUMENTOS';

    return descricao ? (
      <p className="limited-text">{descricao}</p>
    ) : isOutrosDocumentos || this.props.requireDescriptionForAllFiles ? (
      <i className="pi pi-exclamation-triangle ml-2 red-text space-type">Descrição não informada</i>
    ) : (
      <div className="gray-text ml-0">Descrição não informada (opcional)</div>
    );
  }

  _getFormattedSize(showFileType, downloadOnly, fileData) {
    const size = fileData?.tamanho || fileData?.size || fileData?.file?.size;
    return size && <div className="mr-1">{formatBytes(size)}</div>;
    }
  }

  _getFileTypeImage(originUrl, fileType) {
    let fileTypeImage;
    switch (fileType) {
      case 'xls':
        fileTypeImage = `${originUrl}/assets/images/xls.png`;
        break;
      case 'doc':
        fileTypeImage = `${originUrl}/assets/images/doc.png`;
        break;
      case 'pdf':
        fileTypeImage = `${originUrl}/assets/images/pdf.png`;
        break;
      case 'xlsx':
        fileTypeImage = `${originUrl}/assets/images/xlsx.png`;
        break;
      case 'docx':
        fileTypeImage = `${originUrl}/assets/images/docx.png`;
        break;
      case 'csv':
        fileTypeImage = `${originUrl}/assets/images/csv.png`;
        break;
      case 'jpg':
        fileTypeImage = `${originUrl}/assets/images/jpg.png`;
        break;
      case 'png':
        fileTypeImage = `${originUrl}/assets/images/pngImage.png`;
        break;
      case 'dwg':
        fileTypeImage = `${originUrl}/assets/images/dwg.png`;
        break;
      case 'kml':
        fileTypeImage = `${originUrl}/assets/images/kml.png`;
        break;
      default:
        fileTypeImage = `${originUrl}/assets/images/default.png`;
    }
    return fileTypeImage;
  }

  _getFileType(fileName) {
    return fileName.slice(-4) === 'xlsx' || fileName.slice(-4) === 'docx' ? fileName.slice(-4) : fileName.slice(-3);
  }

  _renderListItem(fileData) {
    return this._renderCardItem(fileData, 'p-col-12');
  }

  _renderGridItem(fileData) {
    return this._renderCardItem(fileData, 'p-col-6');
  }

  _itemTemplate(arquivo, layout) {
    if (!arquivo) return null;
    if (layout === 'grid') return this._renderGridItem(arquivo);
    if (layout === 'list') return this._renderListItem(arquivo);
    return null;
  }

  _calculateTotalMB(filteredFiles) {
    const totalMB =
      filteredFiles.reduce((total, file) => {
        const fileSize = file.file?.size || file?.size || 0;
        return total + fileSize;
      }, 0) /
      (1024 * 1024);

    return totalMB.toFixed(2);
  }

  _renderHeader() {
    const { downloadOnly, accept, multi, chooseLabel } = this.props;
    const disabledUpload = downloadOnly || (!multi && this.state.arquivos?.length > 0);
    const chooseLabelText = chooseLabel ?? `Adicionar Arquivo${multi ? 's' : ''}`;
    const disabledByFiles = !(this._getFilteredList()?.length < this.props.max);
    const filteredFiles = this._getFilteredList();
    const totalMBFormatted = this._calculateTotalMB(filteredFiles);

    if (this.state.windowWidth > screenBreakpoints.sm) {
      return (
        <div className="flex justify-content-between">
          {!disabledUpload && (
            <div className="p-col-3 pl-0">
              <div className="flex align-items-center gap-1">
                <FileUpload
                  ref={this.ref}
                  chooseLabel={chooseLabelText}
                  name={`file-uploader-${new Date().toString()}`}
                  accept={accept}
                  mode="basic"
                  auto
                  uploadHandler={(event) => this._uploadHandler(this._normalizeFileType(event))}
                  customUpload
                  multiple
                  disabled={disabledByFiles}
                />
                <HelpIcon documentos={this._sortRequiredFiles(this._getFilteredListEnumArquivos())} />
                {this._renderRequiredFilesIcon()}
                <div className="pl-2">
                  <Tooltip value={'Espaço de armazenamento restante'} sideOffset={0}>
                    <span>{totalMBFormatted ? `${totalMBFormatted}/90MB` : ''}</span>
                  </Tooltip>
                </div>
              </div>
            </div>
          )}
          <DataViewLayoutOptions layout={this.store.selectedLayout} onChange={(e) => this.store.setlayout(e.value)} />
        </div>
      );
    } else {
      return (
        <div className="flex justify-content-between">
          {!disabledUpload && (
            <div className="p-col-3 pl-0">
              <div className="flex align-items-center gap-1">
                <FileUpload
                  ref={this.ref}
                  chooseLabel={chooseLabelText}
                  name={`file-uploader-${new Date().toString()}`}
                  accept={accept}
                  mode="basic"
                  auto
                  uploadHandler={(event) => this._uploadHandler(this._normalizeFileType(event))}
                  customUpload
                  multiple
                  disabled={disabledByFiles}
                />
                <HelpIcon documentos={this._sortRequiredFiles(this._getFilteredListEnumArquivos())} />
                {this._renderRequiredFilesIcon()}
              </div>
            </div>
          )}
        </div>
      );
    }
  }

  _normalizeFileType(event) {
    const arquivos = event.files.map((file) => {
      if (!isValueValid(file.type)) {
        const extension = file.name.split('.').pop().toLowerCase();

        const mimeTypes = {
          dwg: 'image/vnd.dwg',
        };
        const normalizedFile = new File([file], file.name, {
          type: mimeTypes[extension] || 'application/octet-stream',
          lastModified: file.lastModified,
        });
        return normalizedFile;
      }
      return file;
    });

    return { ...event, files: arquivos };
  }

  _renderRequiredFilesIcon() {
    const hasAllRequiredFiles = this.hasAllRequiredFiles();
    if (hasAllRequiredFiles === null) {
      return null;
    }
    return hasAllRequiredFiles ? (
      <Tooltip value="Todos os arquivos obrigatórios foram adicionados">
        <i className="pi pi-check-circle" style={{ color: 'green' }}></i>
      </Tooltip>
    ) : (
      <Tooltip value="Ainda existem arquivos obrigatórios que não foram adicionados">
        <i className="pi pi-exclamation-triangle" style={{ color: 'red' }}></i>
      </Tooltip>
    );
  }

  _renderCards() {
    const filteredFilesList = this._getFilteredList();

    return (
      <DataView
        key={JSON.stringify(this.store.uploadedFiles)}
        header={this._renderHeader()}
        value={filteredFilesList}
        layout={this.store.selectedLayout}
        itemTemplate={this._itemTemplate}
        paginator
        emptyMessage={this.props.emptyMessage}
        rows={20}
        onPage={this._onPageChange}
        first={this.state.currentPage * 20}
      />
    );
  }

  _renderVisualizationDialog() {
    return (
      <Dialog
        visible={this.state.showVisualizationDialog}
        onHide={() => this.setState({ showVisualizationDialog: false, showFileUrl: undefined })}
        style={{ width: '90vw', height: '90vh' }}
        className="show-file-overflow-fix"
        draggable={false}
      >
        {this.state.viewFileUrl && this._renderVisualizationContent()}
      </Dialog>
    );
  }

  _renderVisualizationContent() {
    return <iframe style={{ width: '100%', height: '100%', border: 'none' }} src={this.state.viewFileUrl}></iframe>;
  }

  _uploadHandler(event) {
    const arquivos = [...this.state.arquivos];
    const { isObra, max, faseObra, ShowSaveRemoveNotifications } = this.props;
    const filteredFilesList = isObra ? this._getFilteredListEnumArquivos() : [];

    Object.keys(event.files)
      .slice(0, max)
      .forEach((key) => {
        const baseFileData = {
          key: moment().toISOString() + key,
          arquivo: event.files[key]?.name,
          dataEnvio: moment(),
          file: event.files[key],
          descricao: '',
          fase: this.store.getFase,
        };

        const arq = isObra
          ? {
              ...baseFileData,
              tipo: filteredFilesList?.length === 1 ? filteredFilesList[0].value : '',
              fase: faseObra,
            }
          : {
              ...baseFileData,
              tipo: '',
              tamanho: event.files[key]?.size,
            };

        this.setState({ currentFile: arq });
        this._onUploadFile(arq, () => {
          arquivos.push(arq);
          this.setState({ arquivos }, () => {
            this.forceUpdate();
          });
        });
      });

    event.options?.clear();

    if (ShowSaveRemoveNotifications) {
      const message = event.files.length > 1 ? 'Arquivos adicionados com sucesso!' : 'Arquivo adicionado com sucesso!';
      showNotification('info', null, message);
    }
  }

  _sortRequiredFiles(files, selectedTypes = {}) {
    const originalOrder = this.state.originalOrder;

    const selectedValues = Object.values(selectedTypes);
    const filteredFiles = files.filter((f) => !selectedValues.includes(f.value));

    const sortedFiles = filteredFiles.sort((f1, f2) => {
      const requiredF1 = f1?.text?.startsWith('*');
      const requiredF2 = f2?.text?.startsWith('*');

      if (requiredF1 && !requiredF2) {
        return -1;
      } else if (!requiredF1 && requiredF2) {
        return 1;
      } else {
        return originalOrder[f1.value] - originalOrder[f2.value];
      }
    });

    selectedValues.forEach((selectedType) => {
      const selectedFile = files.find((f) => f.value === selectedType);
      if (selectedFile) {
        sortedFiles.push(selectedFile);
      }
    });

    return sortedFiles;
  }

  render() {
    const { downloadOnly } = this.props;

    if (!this.props.fileTypes?.length || this._getFilteredListEnumArquivos().length > 0 || downloadOnly) {
      return (
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">{this._renderCards()}</div>
          {this._renderVisualizationDialog()}
          {this.state.showDialogDescription && this._renderDescriptionDialog(this.state.currentFile)}
          {this.state.confirmationDialog.show && this._renderConfirmationDialog()}
          {this.state.visibleDialogRemove && this._renderRemoveFileDialog()}
        </div>
      );
    } else {
      return <></>;
    }
  }
}

MultipleFileUploader.defaultProps = {
  downloadOnly: false,
  fileTypes: [],
  showFileType: true,
  accept: '.pdf',
  view: true,
  multi: true,
  ShowSaveRemoveNotifications: false,
  emptyMessage: 'Nenhum arquivo adicionado.',
  max: Number.MAX_SAFE_INTEGER,
  isObra: false,
  requireDescriptionForAllFiles: false,
  fileTypeOptions: {},
};

MultipleFileUploader.propTypes = {
  tags: PropTypes.array,
  store: PropTypes.instanceOf(FileUploadStore).isRequired,
  onChangeFiles: PropTypes.func,
  downloadOnly: PropTypes.bool,
  files: PropTypes.any,
  filterTypes: PropTypes.object,
  fileTypes: PropTypes.array,
  showFileType: PropTypes.bool,
  countDownloadRequest: PropTypes.bool,
  accept: PropTypes.string,
  view: PropTypes.bool,
  multi: PropTypes.bool,
  ShowSaveRemoveNotifications: PropTypes.bool,
  chooseLabel: PropTypes.string,
  emptyMessage: PropTypes.string,
  max: PropTypes.number,
  isObra: PropTypes.bool,
  faseObra: PropTypes.string,
  requireDescriptionForAllFiles: PropTypes.bool,
  fileTypeOptions: PropTypes.object,
};

export default MultipleFileUploader;
