.show-file-overflow-fix {
  .p-dialog-content {
    overflow: hidden !important;
  }
}

.datatable-editing-demo .editable-cells-table td.p-cell-editing {
  padding-top: 0;
  padding-bottom: 0;
}

.card-wrap {
  padding: 0.25rem !important;
}

.card-grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid RGB(220, 220, 220);
  border-radius: 6px;
  position: relative;
  height: 102%;
  background: #fafafa;
  padding: 0;
}

.p-card-content {
  padding: 0 !important;
}

.card-cont-desc {
  overflow: hidden;
  text-overflow: ellipsis;
  flex-direction: row;
  display: flex;
  font-size: 0.9rem;
  color: #a1a1aa;
}

.red-text {
  color: red;
}

.gray-text {
  color: #a1a1aa;
}

.file-info-text {
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: normal;
}

.file-name-text {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: normal;
  margin-top: 0.25rem;
}

.space-type {
  margin-right: 1rem;
}

.selected-option {
  color: #22c55e !important;
}

.card-cont-tipo {
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 0.8rem;
  padding-top: 0.2rem;
}

.card-cont-tipo .p-dropdown {
  width: 280px !important;
}

.card-cont-tipo .p-dropdown-panel {
  width: 300px !important;
}

.card-title {
  color: #24292f;
  font-size: 1.15rem;
  font-weight: bold;
  word-break: break-word;
  align-items: center;
  line-height: 1.3;
}

.dropdown-bord .p-dropdown {
  border: 0;
  height: 2.5rem !important;
}

.dropdown-bord .p-dropdown-label {
  font-size: 0.9rem;
}

.dropdown-bord .p-component .p-inputwrapper {
  max-width: 0%;
}

.limited-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.limited-text:hover {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

.p-dataview.p-dataview-list .p-dataview-content > .p-grid > div {
  border: none;
}

.title-wrapper {
  display: flex;
  align-items: center;
}

.mobile-image {
  display: none;
}

@media screen and (max-width: 1280px) {
  .desktop-image {
    display: none !important;
  }

  .title-wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
  }

  .title-wrapper .card-title {
    flex: 1 !important;
    min-width: 0 !important;
    max-width: calc(100% - 3rem) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .limited-text {
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 3 !important;
    white-space: normal !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
  }

  .mobile-image {
    display: inline-block !important;
  }

  .mobile-image img {
    height: 2rem !important;
    width: auto !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .card-grid {
    display: flex;
    flex-direction: column;
  }

  .card-grid .flex.align-items-center.justify-content-between {
    flex-direction: column;
    width: 100%;
  }

  .card-grid .flex .justify-content-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: clamp(1rem, 5vw, 3rem);
  }

  .date-text {
    padding-top: 0.5rem !important;
  }

  .dropdown-bord {
    min-width: 120px !important;
    flex-shrink: 0 !important;
  }

  .dropdown-bord .p-dropdown {
    width: 100%;
    min-width: 120px !important;
  }
}
