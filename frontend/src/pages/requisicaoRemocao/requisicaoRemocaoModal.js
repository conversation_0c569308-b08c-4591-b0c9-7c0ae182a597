import React from 'react';
import PropTypes from 'prop-types';
import RequisicaoRemocaoFormStore from '~/stores/requisicaoRemocao/formStore';
import FormField from 'fc/components/FormField';
import { observer } from 'mobx-react';
import { InputTextarea } from 'primereact/inputtextarea';
import { getValue, getValueByKey, isValueValid } from 'fc/utils/utils';
import FcButton from 'fc/components/FcButton';
import { Dialog } from 'primereact/dialog';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class RequisicaoRemocaoModal extends GenericFormPage {
  constructor(props) {
    super(props, undefined, AccessPermission.requisicaoModificacao);
    this.store = new RequisicaoRemocaoFormStore();
    this.state = {
      errorDialogValue: false,
    };
    this.validadeDialogAndSubmit = this.validadeDialogAndSubmit.bind(this);
    this._requisicaoRemocao = this._requisicaoRemocao.bind(this);
  }

  validadeDialogAndSubmit() {
    const { processo, tipoProcesso, closeDialog, updateTable } = this.props;
    if (this.store.object.justificativaJurisdicionado) {
      this.setState({ errorDialogValue: false });
      this._requisicaoRemocao(processo.id, tipoProcesso, () => {
        closeDialog();
        this.store.clearObject();
        if (tipoProcesso === 'licitacao') {
          updateTable(processo.id);
        } else {
          updateTable();
        }
      });
    } else {
      this.setState({ errorDialogValue: true });
    }
    this._getFieldErrorMessage(this.state.errorDialogValue);
  }

  _requisicaoRemocao(idProcesso, tipoProcesso, callback) {
    if (tipoProcesso === 'aditivo') {
      this.store.requisicaoRemocaoAditivoContrato(idProcesso, callback);
    } else if (tipoProcesso === 'carona') {
      this.store.requisicaoRemocaoCarona(idProcesso, callback);
    } else if (tipoProcesso === 'contrato') {
      this.store.requisicaoRemocaoContrato(idProcesso, callback);
    } else if (tipoProcesso === 'dispensa') {
      this.store.requisicaoRemocaoDispensa(idProcesso, callback);
    } else if (tipoProcesso === 'inexigibilidade') {
      this.store.requisicaoRemocaoInexigibilidade(idProcesso, callback);
    } else if (tipoProcesso === 'licitacao') {
      this.store.requisicaoRemocaoLicitacao(idProcesso, callback);
    } else if (tipoProcesso === 'credenciamento') {
      this.store.requisicaoRemocaoCredenciamento(idProcesso, callback);
    } else if (tipoProcesso === 'credenciado') {
      this.store.requisicaoRemocaoCredenciado(idProcesso, callback);
    } else if (tipoProcesso === 'diarioObra') {
      this.store.requisicaoRemocaoDiarioObra(idProcesso, callback);
    } else if (tipoProcesso === 'geoobraObra') {
      this.store.requisicaoRemocaoGeoobraObra(idProcesso, callback);
    } else if (tipoProcesso === 'situacaoObra') {
      this.store.requisicaoRemocaoSituacaoObra(idProcesso, callback);
    }
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  _renderNumeroLicitacao(licitacao) {
    return `${
      licitacao.modalidade
        ? getValueByKey(licitacao.modalidade, DadosEstaticosService.getModalidadeLicitacao())
        : getValue(licitacao.modalidadeLicitacaoNova?.nome)
    } - ${licitacao.numero}/${licitacao.ano}`;
  }

  render() {
    let content;
    const { labelProcesso, tipoProcesso, processo } = this.props;

    if (this.store.object) {
      const footer = (
        <div>
          <FcButton
            label="Enviar"
            disabled={!isValueValid(this.store.object.justificativaJurisdicionado)}
            onClick={() => this.validadeDialogAndSubmit()}
            style={{ backgroundColor: '#D32E2E' }}
          />
          <FcButton
            label="Cancelar"
            onClick={() => {
              this.props.closeDialog();
            }}
          />
        </div>
      );

      const label = labelProcesso ?? tipoProcesso;
      const numeroProcesso =
        tipoProcesso == 'licitacao' ? this._renderNumeroLicitacao(processo) : processo && processo.numero;

      content = (
        <>
          <Dialog
            header="Requisição de Remoção"
            visible={this.props.visibleDialog}
            modal
            style={{ width: '50vw' }}
            footer={footer}
            onHide={() => this.props.closeDialog()}
            draggable={false}
            resizable={false}
          >
            <div className="p-fluid p-formgrid p-grid">
              <p className="p-field p-col-12">
                A remoção de {label} só é possível via autorização da auditoria. Envie esta requisição justificando a
                remoção. Se a justificativa for aceita, este(a) {label} será automaticamente removido(a).
              </p>
              {processo && numeroProcesso ? (
                <FormField columns={12} label={'Número de ' + label}>
                  <div className="p-col-12 drawer-content-label">{numeroProcesso}</div>
                </FormField>
              ) : (
                <></>
              )}
              <FormField
                columns={12}
                attribute="justificativa"
                label="Justificativa "
                rule={this.store.getRule('justificativaJurisdicionado')}
              >
                <InputTextarea
                  className={this.store.errorDialogValue ? 'p-invalid p-error' : ''}
                  value={this.store.object.justificativaJurisdicionado}
                  onChange={(e) => {
                    this.store.updateAttribute('justificativaJurisdicionado', e);
                    this.store.justificativaJurisdicionado && this.setState({ errorDialogValue: false });
                  }}
                  placeholder="Informe a justificativa"
                  rows={5}
                  cols={10}
                  maxLength="4000"
                  autoResize
                />
              </FormField>
              <div className="p-col-6">
                {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
              </div>
            </div>
          </Dialog>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return content;
  }
}

RequisicaoRemocaoModal.propTypes = {
  history: PropTypes.any,
  processo: PropTypes.object,
  visibleDialog: PropTypes.bool,
  closeDialog: PropTypes.func,
  updateTable: PropTypes.func,
  tipoProcesso: PropTypes.string,
  labelProcesso: PropTypes.string,
};

export default RequisicaoRemocaoModal;
