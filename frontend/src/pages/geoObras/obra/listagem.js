import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import AccessPermission from '~/constants/AccessPermission';
import { PrimeIcons } from 'primereact/api';
import {
  getValueElipsis,
  getValueMoney,
  getValueDate,
  getValueByKey,
  checkuserContextIsAuditor,
  checkUserContextIsJurisdicionado,
  checkUserGroup,
} from 'fc/utils/utils';
import UrlRouter from '~/constants/UrlRouter';
import ObraIndexStore from '~/stores/geoObras/obra/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import RequisicaoRemocaoModal from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';

@observer
class ObraListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.obra);
    this.store = new ObraIndexStore(this.props.match?.params.id, null);
    this.state = {
      idRemove: null,
      selectedRow: null,
      showRequisicaoRemocao: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocaoModal
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="geoobraObra"
        labelProcesso="Obra"
      />
    );
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  render() {
    const userIsAuditor = checkuserContextIsAuditor();

    const fornecedorContratoObject =
      checkUserContextIsJurisdicionado() || userIsAuditor
        ? {
            field: 'contrato',
            header: 'Fornecedor do Contrato',
            body: ({ contrato }) => getValueElipsis(contrato?.licitante?.nome, 150),
            sortable: true,
          }
        : {};

    const orgaoJuridiscionadoOrigemObject = userIsAuditor
      ? {
          field: 'entidade',
          header: 'Órgão Jurisdicionado de Origem',
          body: ({ entidade }) => getValueElipsis(entidade.nome, 150),
          sortable: true,
        }
      : {};

    const isAuditor = checkUserGroup('Auditor') || checkUserGroup('Administrador');

    const columns = [
      {
        field: 'numero',
        header: 'N° da Obra/Contrato',
        sortable: true,
        body: ({ numeroFormatado, contrato, administracaoDireta }) =>
          administracaoDireta ? numeroFormatado : contrato.numeroContrato,
      },
      {
        field: 'tipo',
        header: 'Origem',
        body: ({ administracaoDireta }) => (
          <span className={`base-badge base-${administracaoDireta ? 'green' : 'blue'}`}>
            {administracaoDireta ? 'ADMINISTRAÇÃO DIRETA' : 'CONTRATO'}
          </span>
        ),
        sortable: true,
      },
      fornecedorContratoObject,
      orgaoJuridiscionadoOrigemObject,
      {
        style: { width: '80%' },
        field: 'descricao',
        header: 'Descrição',
        body: ({ descricao }) => getValueElipsis(descricao, 150),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor da Obra/Contrato',
        body: ({ valor, contrato, administracaoDireta }) =>
          getValueMoney(administracaoDireta ? valor : contrato.aditivo ? contrato.aditivo : contrato.valorGlobal),
        sortable: true,
      },
      {
        field: 'dataPrevistaInicio',
        header: 'Início',
        body: ({ dataPrevistaInicio, dataInicio }) =>
          dataInicio ? getValueDate(dataInicio) : `${getValueDate(dataPrevistaInicio)} (previsão)`,
        sortable: true,
      },
      {
        field: 'dataPrevistaConclusao',
        header: 'Fim',
        body: ({ dataPrevistaConclusao, dataConclusao }) =>
          dataConclusao ? getValueDate(dataConclusao) : `${getValueDate(dataPrevistaConclusao)} (previsão)`,
        sortable: true,
      },
      {
        field: 'statusObra',
        header: 'Situação',
        body: ({ statusObra }) => getValueByKey(statusObra, DadosEstaticosService.getStatusObra()),
        sortable: true,
      },
      {
        field: 'complexoObra',
        header: 'Complexo da Obra',
        body: ({ complexoObra }) => getValueElipsis(complexoObra.nome),
        sortable: true,
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          const getTooltip = (edition = false) => {
            if (rowData.hasRequisicaoModificacao) {
              return `${edition ? 'Edição' : 'Remoção'} desabilitada pois há uma Requisição de Modificação pendente`;
            }

            if (rowData.fase !== 'CADASTRAL') {
              return `Criar Requisição de ${edition ? 'Modificação' : 'Remoção'}`;
            }

            return 'Editar';
          };

          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-book"
                  className="p-button-sm p-mr-2"
                  tooltip="Resumo"
                  onClick={() => this.props.onResumo(rowData)}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  tooltip="Detalhes"
                  onClick={() => this.props.onDetail(rowData)}
                />
              </PermissionProxy>
              {isAuditor && (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-bars"
                    className="p-button-sm p-mr-2"
                    tooltip="Histórico"
                    onClick={() => this.props.onHistory(rowData)}
                  />
                </PermissionProxy>
              )}
              {rowData?.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Ver Requisição de Modificação"
                    className="p-button-sm p-button-info p-mr-1"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                          ':id',
                          rowData.idRequisicaoModificacao
                        )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <PermissionProxy resourcePermissions={this.getWritePermission()}>
                  <div className="flex gap-2">
                    <FcButton
                      icon="pi pi-pencil"
                      className="p-button-sm p-button-success"
                      tooltip={getTooltip(true)}
                      disabled={rowData.hasRequisicaoModificacao}
                      onClick={() =>
                        rowData.fase !== 'CADASTRAL'
                          ? this.props.pushUrlToHistory(
                              UrlRouter.administracao.requisicaoModificacao.obra.requisitar.replace(':id', rowData.id)
                            )
                          : this.props.pushUrlToHistory(UrlRouter.obra.cadastro.editar.replace(':id', rowData.id))
                      }
                    />
                    <FcButton
                      icon="pi pi-trash"
                      className="p-button-sm p-button-danger"
                      onClick={() => {
                        this.setState({ selectedRow: rowData, showRequisicaoRemocao: true });
                      }}
                      tooltip={getTooltip()}
                    />
                  </div>
                </PermissionProxy>
              )}
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.props.pushUrlToHistory(UrlRouter.obra.cadastro.novo)}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroContrato', 'numeroFormatado', 'descricao', 'nomeComplexoObra']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this._renderDialogRequisicaoRemocao()}
        </>
      </PermissionProxy>
    );
  }
}

export default ObraListagemPage;
