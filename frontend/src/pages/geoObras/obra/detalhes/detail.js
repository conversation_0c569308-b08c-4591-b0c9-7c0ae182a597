import React from 'react';
import PropTypes from 'prop-types';
import {
  checkBoolean,
  checkUserGroup,
  formatCep,
  formatDimension,
  generateFullURL,
  getValue,
  getValueBy<PERSON>ey,
  getValueDate,
  getValueElipsis,
  getValueMoney,
} from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
const { observer } = require('mobx-react');
import classNames from 'classnames';
import AppStore from 'fc/stores/AppStore';
import { DATE_FORMAT } from 'fc/utils/date';
import MapaObraIndexStore from '~/stores/geoObras/mapa/indexStore';
import { Divider } from 'primereact/divider';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { Tag } from 'primereact/tag';

@observer
class ObraDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      visibleDialogObras: false,
      showDetalhesDialog: false,
      activeTabIndex: 0,
    };
    this.mapaStore = new MapaObraIndexStore();
    this.mapaStore.setGeoSelected(this.props.obraStore.object.id);
  }

  componentDidMount() {
    this.mapaStore.load({
      andParameters: this.mapaStore.getFilterSuggest(),
    });
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div className="flex flex-column">
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <a href={url} target="_blank" rel="noopener noreferrer" style={{ color: '#3F51B5' }}>
                <u>{value}</u>
              </a>
            }
          </div>
        )}
      </div>
    );
  }

  _renderColumns(columns) {
    return columns.map((col, idx) => <Column className={`p-p-3`} key={`col-${idx}`} {...col} />);
  }

  getGoogleStreetViewUrl() {
    let url = '';
    const idObra = this.mapaStore.geoSelected;
    if (idObra) {
      const obra = this.mapaStore.listKey?.find((o) => o.obra.id == idObra && o.fase === 'INICIAL');
      if (obra) {
        const point = [...obra.startPoint.coordinates];
        url = `http://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${point.reverse().join(',')}`;
      }
    }
    return url;
  }

  _renderTabs(obra) {
    const tabs = [];
    tabs.push({ id: 0, header: 'Arquivos', content: this._renderTabArquivos() });
    if (obra.possuiConveniosAssociados) {
      tabs.push({ id: 1, header: 'Convênios', content: this._renderTabConvenios(obra) });
    }
    if (obra.possuiRecursosProprios) {
      tabs.push({ id: 2, header: 'Recursos Próprios', content: this._renderTabRecursosProprios(obra) });
    }

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderTabRecursosProprios(obra) {
    if (!Array.isArray(obra.recursosProprios)) return null;

    const columns = [
      {
        field: 'objeto',
        header: 'Objeto',
        body: (recursoProprio) => getValueElipsis(recursoProprio.objeto, 150),
        sortable: true,
      },
      {
        field: 'origem',
        header: 'Origem',
        body: (recursoProprio) => getValueElipsis(recursoProprio.origem, 150),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor',
        sortable: true,
        body: (recursoProprio) => getValueMoney(recursoProprio.valor, 2),
      },
    ];
    return (
      <>
        <DataTable value={obra.recursosProprios} className="p-datatable-striped" dataKey="id">
          {this._renderColumns(columns)}
        </DataTable>
      </>
    );
  }

  _renderTabConvenios(obra) {
    const convenios = obra.convenios || [];

    const columns = [
      {
        field: 'numero',
        header: 'Número da Obra/Contrato',
        sortable: true,
      },
      {
        field: 'convenente',
        header: 'Convenente',
        sortable: true,
      },
      {
        field: 'cnpjConvenente',
        header: 'CNPJ do Convenente',
        sortable: true,
      },
      {
        field: 'dataAssinatura',
        header: 'Assinatura',
        sortable: true,
      },
      {
        field: 'dataTermino',
        header: 'Término',
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor do Convênio',
        sortable: true,
        body: (rowData) => getValueMoney(rowData.valor),
      },
      {
        field: 'percentualContrapartida',
        header: 'Percentual da Contrapartida',
        sortable: true,
        body: (rowData) => getValue(rowData.percentualContrapartida) + '%',
      },
    ];

    return (
      <>
        <DataTable value={convenios} className="p-datatable-striped" dataKey="id">
          {this._renderColumns(columns)}
        </DataTable>
      </>
    );
  }

  _renderTabArquivos() {
    const { obraStore } = this.props;
    return (
      <div className="p-fluid p-formgrid">
        <MultipleFileUploader
          isObra
          downloadOnly
          store={obraStore.fileStore}
          fileTypes={DadosEstaticosService.getTipoArquivoObra()}
          showFileType
          filterTypes={{
            filter: {
              column: 'fase',
              values: ['CADASTRAL'],
            },
          }}
        />
      </div>
    );
  }

  getTagTimelineObra(obra, intervalo, data) {
    const dataIntervalo = new Date(data);
    const inicioVigencia = new Date(obra.contrato.dataVigenciaInicial);
    const fimVigencia = new Date(obra.contrato.dataVigenciaFinal);
    if (dataIntervalo < inicioVigencia) {
      return <Tag value={`${intervalo} Antecipado(a)`} severity="info" />;
    }
    if (dataIntervalo <= fimVigencia) {
      return <Tag value={`${intervalo} dentro da vigência`} severity="success" />;
    }
    return <Tag value={`${intervalo} após o fim da vigência`} severity="danger" />;
  }

  renderPanelDadosObra = (obra) => {
    let linkContrato = null;

    if (!obra.administracaoDireta) {
      linkContrato = {
        link: generateFullURL(UrlRouter.cadastrosConsulta.contrato.detalhe.replace(':id', obra.contrato.id)),
        text: `Contrato ${obra.contrato.numero + '/' + obra.contrato.anoContrato ?? ''}`,
        permission: AccessPermission.contrato.readPermission,
      };
    }
    return (
      <div className="p-fluid p-formgrid">
        <div className="p-d-flex p-jc-between p-ai-center p-mb-3">
          {this._renderDivider('Detalhes da Obra', `obra-${obra.id}`)}
        </div>
        {this._renderValue(
          'Número da Obra/Contrato',
          getValue(obra.administracaoDireta ? obra.numeroFormatado : obra.contrato.numeroContrato)
        )}
        {this._renderValue('Data de Cadastro', getValueDate(obra.dataCadastro, DATE_FORMAT))}
        {this._renderValue('Data Prevista de Início', getValueDate(obra.dataPrevistaInicio, DATE_FORMAT))}
        {this._renderValue('Período Previsto para Conclusão da Obra', obra.previsaoConclusao)}
        {this._renderValue('Data Prevista de Conclusão', getValueDate(obra.dataPrevistaConclusao, DATE_FORMAT))}
        {this._renderValue('Data de Emissão da Ordem de Serviço', getValueDate(obra.dataEmissaoServico, DATE_FORMAT))}
        {this._renderValue('A Obra é Incoporável ao Patrimônio', getValue(checkBoolean(obra.incorporavelPatrimonio)))}
        {this._renderValue('A Obra Possui Apenas Anteprojeto', getValue(checkBoolean(obra.possuiApenasAnteprojeto)))}

        {checkUserGroup(['Administrador', 'Auditor']) &&
          this._renderValue(
            'Prazo de Execução Previsto',
            `${getValueDate(obra.dataPrevistaInicio, DATE_FORMAT)} - ${getValueDate(
              obra.dataPrevistaConclusao,
              DATE_FORMAT
            )} (${obra.previsaoConclusao})`
          )}
        {checkUserGroup(['Administrador', 'Auditor']) &&
          this._renderValue(
            'Prazo de Execução Real',
            `${obra.dataInicio ? getValueDate(obra.dataInicio, DATE_FORMAT) : 'Não iniciou'} - ${
              obra.dataConclusao ? getValueDate(obra.dataConclusao, DATE_FORMAT) : 'Não finalizou'
            }`
          )}
        {this._renderValue(
          'Valor da Obra/Contrato',
          getValueMoney(
            obra.administracaoDireta
              ? obra.valor
              : obra.contrato.aditivo
              ? obra.contrato.aditivo
              : obra.contrato.valorGlobal
          )
        )}
        {this._renderValue('Município', obra.municipio.nomeMun)}
        {this._renderValue(
          'Endereços',
          getValue(obra.enderecos?.map((e) => `${e.endereco} | ${formatCep(e.cep)}`)),
          12,
          'list'
        )}
        {this._renderValue('Street View ', 'Google Maps', 12, 'link', this.getGoogleStreetViewUrl())}
        {this._renderValue('Descrição', getValue(obra.descricao))}
        {obra.administracaoDireta
          ? this._renderValue('Origem', 'ADMINISTRAÇÃO DIRETA')
          : this._renderValue('Origem', linkContrato.text, 12, 'link', linkContrato.link)}
        {this._renderValue('Status', getValueByKey(obra.statusObra, DadosEstaticosService.getStatusObra()))}
        {checkUserGroup(['Administrador', 'Auditor']) &&
          !obra.administracaoDireta &&
          obra.dataInicio &&
          this._renderValue(
            'Status da data de início em relação ao Contrato',
            this.getTagTimelineObra(obra, 'Início', obra.dataInicio)
          )}
        {checkUserGroup(['Administrador', 'Auditor']) &&
          !obra.administracaoDireta &&
          obra.dataConclusao &&
          this._renderValue(
            'Status da data de conclusão em relação ao Contrato',
            this.getTagTimelineObra(obra, 'Conclusão', obra.dataConclusao)
          )}
        {this._renderValue('Complexo de Obras', getValue(obra.complexoObra.nome))}
        {this._renderValue('Tipo', getValueByKey(obra.tipo, DadosEstaticosService.getTipoObra(), 'value', 'label'))}
        {this._renderValue(
          'Classificações',
          getValue(
            obra.classificacoes?.map(
              (c) =>
                `${getValueByKey(
                  c.classificacao,
                  DadosEstaticosService.getClassificacaoObra(obra.tipo),
                  'value',
                  'label'
                )}`
            )
          ),
          12,
          'list'
        )}
        {this._renderValue('Dimensão', formatDimension(obra.valorDimensao, obra.tipoDimensao))}
        {this._renderValue('Responsável Técnico de Projeto', obra.responsavelTecnicoProjeto)}
        {this._renderValue('ART ou RRT - Responsável pelo Projeto', obra.numeroArtProjeto)}
        {this._renderValue('Responsável Técnico de Execução', obra.responsavelTecnicoExecucao)}
        {this._renderValue('ART ou RRT - Responsável pela Execução', obra.numeroArtExecucao)}
        {obra.administracaoDireta ? (
          <>
            <div className="p-d-flex p-jc-between p-ai-center p-mb-3" id={`gestoresFiscaisObra-${obra.id}`}>
              {this._renderDivider('Gestor e Fiscal da Obra')}
            </div>
            {this._renderValue('Gestor', getValue(obra.nomeGestor))}
            {this._renderValue('Fiscal', getValue(obra.nomeFiscal))}
          </>
        ) : (
          <>
            <div className="p-d-flex p-jc-between p-ai-center p-mb-3" id={`gestoresFiscais-${obra.contrato.id}`}>
              {this._renderDivider('Detalhes do Contrato')}
            </div>
            {checkUserGroup('Auditor') &&
              this._renderValue('Data de Cadastro', getValueDate(obra.contrato.dataCadastro, DATE_FORMAT))}
            {this._renderValue('Gestor', obra.contrato.gestorContrato || obra.contrato.nomeGestorCjur)}
            {this._renderValue(
              'Gestor Substituto',
              obra.contrato.gestorSuplente || obra.contrato.nomeGestorSubstitutoCjur
            )}
            {this._renderValue('Fiscal', obra.contrato.fiscalTitular || obra.contrato.nomeFiscalCjur)}
            {this._renderValue(
              'Fiscal Substituto',
              obra.contrato.fiscalSuplente || obra.contrato.nomeFiscalSubstitutoCjur
            )}
          </>
        )}
      </div>
    );
  };

  _renderDivider(label) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  render() {
    const { obraStore } = this.props;
    const obra = obraStore.object;
    return (
      <div>
        {obra && (
          <>
            {this.renderPanelDadosObra(obra)}
            <Divider style={{ marginBottom: `1px` }} />
            {this._renderTabs(obra)}
          </>
        )}
      </div>
    );
  }
}

ObraDetailPage.propTypes = {
  obraStore: PropTypes.any,
};

export default ObraDetailPage;
