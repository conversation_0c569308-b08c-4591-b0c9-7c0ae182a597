import AdvancedSearch from 'fc/components/AdvancedSearch';
import Fc<PERSON>utton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import ComplexoObrasIndexStore from '~/stores/geoObras/complexoObra/indexStore';
import './style.scss';
import PermissionProxy from 'fc/components/PermissionProxy';
import { PrimeIcons } from 'primereact/api';
import ComplexoObraIndexDetailPage from '../../complexoObra/detalhes/indexDetail';
import PropTypes from 'prop-types';
import FormField from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import ComplexoObraFormStore from '~/stores/geoObras/complexoObra/formStore';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class TabComplexoObras extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.complexoObra);
    this.store = new ComplexoObrasIndexStore();
    this.storeFormComplexoObras = new ComplexoObraFormStore();
    this.storeFormComplexoObras.initialize(null);

    this.state = {
      submitted: false,
      detalhesVisibility: false,
      dialogCriarComplexoVisibily: false,
      selectedRow: null,
      submittedItem: null,
    };
  }

  renderDetalhesDialog() {
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Detalhes</h4>
      </div>
    );

    return (
      <Dialog
        header={header}
        visible={this.state.detalhesVisibility}
        style={{ width: '80vw' }}
        onHide={() => this.setState({ detalhesVisibility: false })}
        draggable={false}
        resizable={false}
        dismissableMask={false}
      >
        <ComplexoObraIndexDetailPage id={this.state.selectedRow.id} isDialog={true} />
      </Dialog>
    );
  }

  renderCriarNovoComplexoDialog() {
    const { updateAttribute, getRule } = this.storeFormComplexoObras;
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Criar Novo Complexo de Obras</h4>
      </div>
    );

    return (
      <Dialog
        blockScroll
        header={header}
        visible={this.state.dialogCriarComplexoVisibily}
        style={{ width: '50vw' }}
        onHide={() => this.setState({ dialogCriarComplexoVisibily: false })}
        draggable={false}
        resizable={false}
        dismissableMask={false}
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  this.storeFormComplexoObras.cleanObjeto();
                  this.setState({ dialogCriarComplexoVisibily: false });
                }}
              />
              <FcButton
                label="Salvar"
                type="submit"
                loading={this.store.loading}
                onClick={async () => {
                  this.setState({ submitted: true });
                  if (!this.storeFormComplexoObras.rules.hasError) {
                    await this.storeFormComplexoObras.save(() => {
                      this.store.load();
                    }, 'new');
                    await this.setState({
                      dialogCriarComplexoVisibily: false,
                      submitted: false,
                      submittedItem: {
                        nome: this.storeFormComplexoObras.object.nome,
                        descricao: this.storeFormComplexoObras.object.descricao,
                      },
                    });
                  }
                }}
              />
            </span>
          </div>
        }
      >
        <div className="p-col-12">
          <form>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="nome"
                label="Nome"
                rule={getRule('nome')}
                submitted={this.state.submitted}
              >
                <InputText
                  onChange={(e) => {
                    updateAttribute('nome', e);
                  }}
                  placeholder="Informe o nome do complexo"
                  value={this.storeFormComplexoObras.object.nome}
                  id="nome"
                />
              </FormField>
              <FormField
                columns={12}
                attribute="descricao"
                label="Descrição"
                rule={getRule('descricao')}
                submitted={this.state.submitted}
              >
                <FcInputTextarea
                  id="descricao"
                  placeholder="Informe a descrição do complexo de obras"
                  rows={6}
                  cols={30}
                  value={this.storeFormComplexoObras.object.descricao}
                  onChange={(e) => {
                    updateAttribute('descricao', e);
                  }}
                />
              </FormField>
            </div>
          </form>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'descricao',
        header: 'Descrição',
        sortable: true,
      },
      {
        field: 'quantidadeObrasAssociadas',
        header: 'Quantidade de Obras Associadas',
        sortable: true,
      },

      {
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-list"
                tooltip="Visualizar"
                className="p-button-bg p-button-info p-mr-2"
                onClick={() => this.setState({ detalhesVisibility: true, selectedRow: rowData })}
              />
              <FcButton
                icon="pi pi-arrow-circle-right"
                tooltip="Selecionar"
                className="p-button-bg p-button-success p-mr-2"
                onClick={() => this.props.onSelect(rowData)}
              />
            </div>
          );
        },
      },
    ];
    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const IndexDataTableHeader = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Criar Novo Complexo de Obras"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.setState({ dialogCriarComplexoVisibily: true })}
          />
        </PermissionProxy>
      </div>
    );

    return (
      <div className="card page index-table">
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['nome', 'descricao']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <IndexDataTable
          columns={columns}
          getPreviousFiltersValues={false}
          value={listKey}
          loading={loading}
          header={IndexDataTableHeader}
          highlightOnSelection
          idSelected={this.props.idComplexoObra}
          {...getDefaultTableProps()}
        />
        {this.state.detalhesVisibility && this.renderDetalhesDialog()}
        {this.state.dialogCriarComplexoVisibily && this.renderCriarNovoComplexoDialog()}
      </div>
    );
  }
}

TabComplexoObras.displayName = 'TabComplexoObras';

TabComplexoObras.propTypes = {
  idComplexoObra: PropTypes.any,
};

export default TabComplexoObras;
