import classNames from 'classnames';
import AppStore from 'fc/stores/AppStore';
import { checkUserGroup, formatCep, getValue, getValue<PERSON>y<PERSON><PERSON>, getValueMoney } from 'fc/utils/utils';
import { Divider } from 'primereact/divider';
import { Tag } from 'primereact/tag';
import PropTypes from 'prop-types';
import React from 'react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import MedicaoIndexStore from '~/stores/geoObras/acompanhamento/medicao/indexStore';
import MapaObraIndexStore from '~/stores/geoObras/mapa/indexStore';
import { observer } from 'mobx-react';
import './style.scss';

@observer
class ObraResumoPage extends React.Component {
  constructor(props) {
    super(props);
    this.mapaStore = new MapaObraIndexStore();
    this.mapaStore.setGeoSelected(this.props.obra.id);
    this.medicaoIndexStore = new MedicaoIndexStore();
    this.state = {
      percentualConclusao: null,
    };
  }

  componentDidMount() {
    const promises = [];
    promises.push(
      this.mapaStore.load({
        andParameters: this.mapaStore.getFilterSuggest(),
      })
    );
    promises.push(
      this.medicaoIndexStore.carregarPorObra(this.props.obra.id, () => {
        if (this.medicaoIndexStore.medicoes.length > 0) {
          const ultimaMedicao = this.medicaoIndexStore.medicoes.slice(-1)[0];
          const { percentualConclusao } = ultimaMedicao;
          this.setState({ percentualConclusao });
        } else {
          this.setState({ percentualConclusao: 0 });
        }
      })
    );
    Promise.all(promises);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `Resumos-set Resumos-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 Resumos-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 Resumos-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div className="flex flex-column">
            {value?.map((value) => (
              <div className={`Resumos-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 Resumos-value p-text-justify`}
          >
            {
              <a href={url} target="_blank" rel="noopener noreferrer" style={{ color: '#3F51B5' }}>
                <u>{value}</u>
              </a>
            }
          </div>
        )}
      </div>
    );
  }

  getGoogleStreetViewUrl() {
    let url = '#';
    const idObra = this.mapaStore.geoSelected;
    if (idObra) {
      const obra = this.mapaStore.listKey?.find((o) => o.obra.id == idObra && o.fase === 'INICIAL');
      if (obra) {
        const point = [...obra.startPoint.coordinates];
        url = `http://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${point.reverse().join(',')}`;
      }
    }
    return url;
  }

  getTagTimelineObra(obra, intervalo, artigo, data) {
    const dataIntervalo = new Date(data);
    const inicioVigencia = new Date(obra.contrato.dataVigenciaInicial);
    const fimVigencia = new Date(obra.contrato.dataVigenciaFinal);
    if (dataIntervalo < inicioVigencia) {
      return <Tag value={`${intervalo} Antecipad${artigo}`} severity="info" />;
    }
    if (dataIntervalo <= fimVigencia) {
      return <Tag value={`${intervalo} dentro da vigência`} severity="success" />;
    }
    return <Tag value={`${intervalo} após o fim da vigência`} severity="danger" />;
  }

  _renderDivider(label) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _renderPercentualConclusao(percentualConclusao) {
    return (
      <div className="percentual-conclusao-container">
        <div className="barra">
          <div style={{ width: `${percentualConclusao}%` }} />
        </div>
        <p>{percentualConclusao}%</p>
      </div>
    );
  }

  render() {
    const { obra } = this.props;
    return (
      <div className="p-fluid p-formgrid">
        {!obra.administracaoDireta && (
          <>
            <div className="p-d-flex p-jc-between p-ai-center p-mb-3">
              {this._renderDivider('Contrato', `obra-${obra.id}`)}
            </div>
            {this._renderValue('Número do Contrato', getValue(obra.contrato.numeroContrato))}
            {this._renderValue('Valor inicial do Contrato', getValueMoney(obra.contrato.valorGlobal))}
            {this._renderValue('Valor atual do Contrato', getValueMoney(obra.contrato.aditivo))}
            {checkUserGroup(['Administrador', 'Auditor']) && (obra.dataInicio || obra.dataConclusao) && (
              <>
                <h6 style={{ padding: '6px' }}>Vigência</h6>
                {obra.dataInicio &&
                  this._renderValue(
                    'Status da data de início em relação ao Contrato',
                    this.getTagTimelineObra(obra, 'Início', 'o', obra.dataInicio)
                  )}
                {obra.dataConclusao &&
                  this._renderValue(
                    'Status da data de conclusão em relação ao Contrato',
                    this.getTagTimelineObra(obra, 'Conclusão', 'a', obra.dataConclusao)
                  )}
              </>
            )}
          </>
        )}
        <div className="p-d-flex p-jc-between p-ai-center p-mb-3">
          {this._renderDivider('Detalhes da Obra', `obra-${obra.id}`)}
        </div>
        {obra.administracaoDireta && (
          <>
            {this._renderValue('Número da Obra', getValue(obra.numeroFormatado))}
            {this._renderValue('Valor da Obra', getValueMoney(obra.valor))}
          </>
        )}
        {this._renderValue('Descrição', getValue(obra.descricao))}
        {this._renderValue('Street View ', 'Google Maps', 12, 'link', this.getGoogleStreetViewUrl())}
        {this._renderValue(
          'Endereços',
          getValue(obra.enderecos?.map((e) => `${e.endereco} | ${formatCep(e.cep)}`)),
          12,
          'list'
        )}
        {this._renderDivider('Recursos Próprios', `obra-${obra.id}`)}
        {this._renderValue(
          'Valor Total em Recursos Próprios',
          getValueMoney(obra.recursosProprios.reduce((total, recursoProprio) => total + recursoProprio.valor, 0))
        )}
        {this._renderValue('Quantidade de Recursos Próprios', obra.recursosProprios.length ?? 0)}
        {this._renderDivider('Convênios', `obra-${obra.id}`)}
        {this._renderValue(
          'Valor Total em Convênios',
          getValueMoney(obra.convenios.reduce((total, convenio) => total + convenio.valor, 0))
        )}
        {this._renderValue('Quantidade de Convênios', obra.recursosProprios.length ?? 0)}
        {this._renderDivider('Dados Gerais', `obra-${obra.id}`)}
        {this._renderValue('Status', getValueByKey(obra.statusObra, DadosEstaticosService.getStatusObra()))}
        {this.state.percentualConclusao != null &&
          this._renderValue('Percentual de Conclusão', this._renderPercentualConclusao(this.state.percentualConclusao))}
      </div>
    );
  }
}

ObraResumoPage.propTypes = {
  obra: PropTypes.any,
};

export default ObraResumoPage;
