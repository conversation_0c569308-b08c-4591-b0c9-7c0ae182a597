import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import AccessPermission from '~/constants/AccessPermission';
import { PrimeIcons } from 'primereact/api';
import { getValueMoney, getValueDate, getNumberFractionDigits, getValue } from 'fc/utils/utils';
import UrlRouter from '~/constants/UrlRouter';
import IndexDataTable from 'fc/components/IndexDataTable';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import MedicaoIndexStore from '~/stores/geoObras/acompanhamento/medicao/indexStore';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import ObraFormStore from '~/stores/geoObras/obra/formStore';
import SituacaoObraIndexStore from '~/stores/geoObras/acompanhamento/situacaoObra/indexStore';

@observer
class MedicaoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.acompanhamento);
    this.store = new MedicaoIndexStore(this.props.match.params.idObra);
    this.obraFormStore = new ObraFormStore();
    this.situacaoObraIndexStore = new SituacaoObraIndexStore(this.props.match.params.idObra);
    this.store.setIdObra(this.props.match.params.idObra);

    this.state = {
      idRemove: null,
      formDialogVisible: false,
      medicaoObraId: '',
      hasRequisicaoModificacao: null,
    };
  }

  componentDidMount() {
    const idObra = this.props.match.params.idObra;
    this.store.carregarPorObra(idObra);
    this.store.recuperarUltimaMedicaoObra(idObra);
    this.obraFormStore.initialize(idObra);
    this.situacaoObraIndexStore.recuperarUltimaSituacaoObra(idObra);
    this.obraFormStore.hasRequisicaoModificacao(idObra, (hasReq) =>
      this.setState({ hasRequisicaoModificacao: hasReq })
    );
  }

  disableCreationAndEditing(disableAt100Percent = true, medicaoId = null) {
    const percentualConclusao = this.store.ultimaMedicaoObra?.percentualConclusao;
    const ultimaMedicaoObraId = this.store.ultimaMedicaoObra?.id;
    const fase = this.obraFormStore.object?.fase;
    const status = this.situacaoObraIndexStore.ultimaSituacaoObra?.statusObra;

    return (
      (disableAt100Percent && percentualConclusao >= 100) ||
      fase !== 'MEDICAO' ||
      !['ATRASADA', 'EXECUCAO_NORMAL', 'REINICIADA'].includes(status) ||
      (medicaoId && medicaoId !== ultimaMedicaoObraId)
    );
  }

  render() {
    const { title } = this.props;
    const columns = [
      {
        field: 'dataInicio',
        header: 'Data de Início',
        body: ({ dataInicio }) => getValueDate(dataInicio, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'dataFim',
        header: 'Data de Fim',
        body: ({ dataFim }) => getValueDate(dataFim, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valor',
        header: 'Valor',
        body: ({ valor }) => getValueMoney(valor),
      },
      {
        field: 'percentualConclusao',
        header: 'Percentual de Conclusão',
        body: ({ percentualConclusao }) => `${getNumberFractionDigits(getValue(percentualConclusao), 2)}%`,
      },
      {
        field: 'empenho',
        header: 'Número do Empenho',
        style: { width: '10%' },
        body: ({ empenho }) => empenho?.numeroEmpenho ?? '-',
      },
      {
        body: (rowData) => {
          const getTooltip = () => {
            if (this.state.hasRequisicaoModificacao) {
              return 'Edição desabilitada pois há uma Requisição de Modificação pendente';
            }

            if (this.disableCreationAndEditing(false, rowData.id)) {
              return 'Criar Requisição de Modificação';
            }
            return 'Editar';
          };

          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  onClick={() => this.props.onDetail(rowData)}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                {rowData?.idRequisicaoModificacao ? (
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Ver Requisição de Modificação"
                    className="p-button-sm p-button-info p-mr-2"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                          ':id',
                          rowData.idRequisicaoModificacao
                        )
                      )
                    }
                  />
                ) : (
                  <FcButton
                    icon="pi pi-pencil"
                    className="p-button-sm p-button-success p-mr-2"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.obra.acompanhamento.medicoes.editar
                          .replace(':idMedicao', rowData.id)
                          .replace(':idObra', this.props.match.params.idObra)
                      )
                    }
                    tooltip={getTooltip()}
                    disabled={this.state.hasRequisicaoModificacao}
                  />
                )}
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() =>
              this.pushUrlToHistory(
                UrlRouter.obra.acompanhamento.medicoes.novo.replace(':idObra', this.props.match.params.idObra)
              )
            }
            disabled={this.disableCreationAndEditing() || this.state.hasRequisicaoModificacao}
          />
        </PermissionProxy>
      </div>
    );

    const { getDefaultTableProps } = this;
    const { listKey, loading } = this.store;

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <h2>{title}</h2>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['valor', 'percentualConclusao']}
          typesSearchFields={['number', 'number']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <IndexDataTable
          columns={columns}
          value={listKey}
          header={header}
          loading={loading}
          disableColumnToggle
          {...getDefaultTableProps()}
        />
      </PermissionProxy>
    );
  }
}

export default MedicaoListagemPage;
