import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import { getValueDate, getValueElipsis } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import DiarioObraIndexStore from '~/stores/geoObras/diario/indexStore';
import DiarioObraFormPage from './form';
import ObraFormStore from '~/stores/geoObras/obra/formStore';
import SituacaoObraIndexStore from '~/stores/geoObras/acompanhamento/situacaoObra/indexStore';
import RequisicaoRemocaoModal from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import UrlRouter from '~/constants/UrlRouter';

@observer
class DiarioObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.acompanhamento);
    this.store = new DiarioObraIndexStore(props.obraId);
    this.obraFormStore = new ObraFormStore();
    this.situacaoObraIndexStore = new SituacaoObraIndexStore(props.obraId);
    this.state = {
      idRemove: null,
      formDialogVisible: false,
      diarioObraId: '',
      readOnly: false,
      selectedRow: null,
      showRequisicaoRemocao: false,
      hasRequisicaoModificacao: null,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);

    this.store.load();
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocaoModal
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="diarioObra"
        labelProcesso="Diário de Obra"
      />
    );
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  componentDidMount() {
    this.obraFormStore.initialize(this.props.obraId);
    this.situacaoObraIndexStore.recuperarUltimaSituacaoObra(this.props.obraId);
    this.store.recuperarUltimoDiarioObra(this.props.obraId);
    this.obraFormStore.hasRequisicaoModificacao(this.props.obraId, (hasReq) =>
      this.setState({ hasRequisicaoModificacao: hasReq })
    );
  }

  renderFormDialog() {
    const isEdit = !!this.state.diarioObraId;
    return (
      <Dialog
        visible={this.state.formDialogVisible}
        header={`${isEdit ? 'Editar' : 'Adicionar'} Diário`}
        style={{ width: '50vw' }}
        onHide={() => {
          this.setState({ formDialogVisible: false });
        }}
      >
        <DiarioObraFormPage
          obraId={this.props.obraId}
          id={this.state.diarioObraId}
          readOnly={this.state.readOnly}
          onHide={() => {
            this.setState({ formDialogVisible: false });
          }}
          onSaveOrEdit={() => {
            this.store.recuperarUltimoDiarioObra(this.props.obraId, () => this.store.load());
            this.obraFormStore.hasRequisicaoModificacao(this.props.obraId, (hasReq) =>
              this.setState({ hasRequisicaoModificacao: hasReq })
            );
          }}
          action={isEdit ? 'edit' : 'new'}
        />
      </Dialog>
    );
  }

  confirmRemove(id) {
    const { deleteRow } = this.store;

    return (
      <ConfirmDialog
        blockScroll
        visible={this.store.isConfirmDialogVisible}
        message={<div className="pl-2">{'Você realmente deseja excluir o registro selecionado?'}</div>}
        header="Excluir registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => deleteRow(id, () => this.store.load())}
        onHide={() => this.store.toggleShowConfirmDialog()}
      />
    );
  }

  enableForm({ formDialogVisible = false, diarioObraId = '', readOnly = false }) {
    this.setState({ formDialogVisible, diarioObraId, readOnly });
  }

  disableCreationAndEditing(diarioId = null) {
    const fase = this.obraFormStore.object?.fase;
    const status = this.situacaoObraIndexStore.ultimaSituacaoObra?.statusObra;
    const ultimoDiarioObraId = this.store.ultimoDiarioObra?.id;

    return (
      (fase && fase !== 'MEDICAO') ||
      (status && !['ATRASADA', 'EXECUCAO_NORMAL', 'REINICIADA'].includes(status)) ||
      (diarioId && ultimoDiarioObraId && diarioId !== ultimoDiarioObraId)
    );
  }

  render() {
    const columns = [
      {
        field: 'dataInicioDiario',
        header: 'Data Inicial do Diário',
        style: { width: '135px' },
        body: ({ dataInicioDiario }) => getValueDate(dataInicioDiario, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'dataFimDiario',
        header: 'Data Final do Diário',
        style: { width: '135px' },
        body: ({ dataFimDiario }) => getValueDate(dataFimDiario, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'observacoes',
        header: 'Observações',
        body: ({ observacoes }) => getValueElipsis(observacoes, 50),
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          const getTooltip = (edition = false) => {
            if (this.state.hasRequisicaoModificacao) {
              return `${edition ? 'Edição' : 'Remoção'} desabilitada pois há uma Requisição de Modificação pendente`;
            }

            if (this.disableCreationAndEditing(rowData.id)) {
              return `Criar Requisição de ${edition ? 'Modificação' : 'Remoção'}`;
            }

            return 'Editar';
          };

          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  onClick={() => this.enableForm({ formDialogVisible: true, diarioObraId: rowData.id, readOnly: true })}
                />
              </PermissionProxy>
              {rowData?.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Ver Requisição de Modificação"
                    className="p-button-sm p-button-info p-mr-1"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                          ':id',
                          rowData.idRequisicaoModificacao
                        )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <div className="flex">
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-pencil"
                      tooltip={getTooltip(true)}
                      className="p-button-sm p-button-success p-mr-2"
                      onClick={() => this.enableForm({ formDialogVisible: true, diarioObraId: rowData.id })}
                      disabled={this.state.hasRequisicaoModificacao}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-trash"
                      className="p-button-sm p-button-danger"
                      tooltip={getTooltip()}
                      onClick={() => {
                        this.setState({ selectedRow: rowData, showRequisicaoRemocao: true });
                      }}
                      disabled={this.state.hasRequisicaoModificacao}
                    />
                  </PermissionProxy>
                </div>
              )}
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.enableForm({ formDialogVisible: true })}
            disabled={this.disableCreationAndEditing() || this.state.hasRequisicaoModificacao}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <IndexDataTable
          columns={columns}
          value={listKey}
          header={header}
          loading={loading}
          disableColumnToggle
          {...getDefaultTableProps()}
        />
        {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        {this.renderFormDialog()}
        {this._renderDialogRequisicaoRemocao()}
      </PermissionProxy>
    );
  }
}

DiarioObraIndexPage.displayName = 'DiarioObraIndexPage';

export default DiarioObraIndexPage;
