import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import SituacaoObraIndexStore from '~/stores/geoObras/acompanhamento/situacaoObra/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getValue, getValueByKey } from 'fc/utils/utils';
import FcButton from 'fc/components/FcButton';
import UrlRouter from '~/constants/UrlRouter';
import { PrimeIcons } from 'primereact/api';
import moment from 'moment';
import RequisicaoRemocaoModal from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import ObraFormStore from '~/stores/geoObras/obra/formStore';

@observer
class SituacaoObraListagemPage extends GenericIndexPage {
  idObra;
  constructor(props) {
    super(props, AccessPermission.geoObras.acompanhamento);
    this.store = new SituacaoObraIndexStore(this.props.match?.params?.idObra);
    this.obraFormStore = new ObraFormStore(this.props.match?.params?.idObra);

    this.state = {
      idRemove: null,
      selectedRow: null,
      showRequisicaoRemocao: false,
      hasRequisicaoModificacao: null,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  componentDidMount() {
    this.idObra = this.props.match?.params?.idObra;
    this.store.recuperarUltimaSituacaoObra(this.idObra);
    this.obraFormStore.hasRequisicaoModificacao(this.idObra, (hasReq) =>
      this.setState({ hasRequisicaoModificacao: hasReq })
    );
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocaoModal
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="situacaoObra"
        labelProcesso="Situação de Obra"
      />
    );
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderContagemParalisacao(now, dataInicioParalisacao) {
    const calcularDiasParalisado = (inicio) => {
      const diferenca = now - new Date(inicio);
      return Math.floor(diferenca / (1000 * 60 * 60 * 24));
    };

    const dias = calcularDiasParalisado(dataInicioParalisacao);
    let label = `Obra paralisada há ${dias} dia${dias > 1 ? 's' : ''}`;

    if (dias === 0) {
      label = `Obra paralisada há menos de 1 dia`;
    }

    return (
      <div
        className="p-button disable-cursor"
        style={{ marginBottom: '5px', marginRight: '5px', height: '34px' }}
        disabled
      >
        {label}
      </div>
    );
  }

  hasObraFinalizada() {
    const { statusObra: status = '', motivo = '' } = this.store.ultimaSituacaoObra || {};

    return (
      status === 'INTERROMPIDA' || (status === 'CONCLUIDA' && ['RECEBIDA_DEFINITIVO', 'NAO_RECEBIDA'].includes(motivo))
    );
  }

  checkRequisicaoModificacao(dataCadastro) {
    if (!dataCadastro) return true;

    const now = moment();

    const cadastro = moment(dataCadastro);
    const horasDecorridas = now.diff(cadastro, 'hours', true);
    const passou24h = horasDecorridas > 24;

    const obraFinalizada = this.hasObraFinalizada();

    const mesmaJanela = cadastro.isSame(now, 'month');

    return !mesmaJanela || (obraFinalizada && passou24h);
  }

  render() {
    const columns = [
      {
        field: 'dataCadastro',
        header: 'Mês/Ano',
        sortable: true,
        body: ({ dataCadastro }) => moment(dataCadastro).format('MM/YYYY'),
      },
      {
        field: 'statusObra',
        header: 'Status da Obra',
        sortable: true,
        body: ({ statusObra }) => getValueByKey(statusObra, DadosEstaticosService.getTipoStatusSituacaoObra(true)),
      },
      {
        field: 'definidoSistema',
        header: 'Origem',
        body: ({ definidoSistema }) => (
          <span className={`base-badge base-${definidoSistema ? 'green' : 'blue'}`}>
            {definidoSistema ? 'SISTEMA' : 'USUÁRIO'}
          </span>
        ),
        sortable: true,
      },
      {
        field: 'motivo',
        header: 'Motivo',
        sortable: true,
        body: ({ motivo }) => getValueByKey(motivo, DadosEstaticosService.getTodosMotivosSituacaoObra()),
      },
      {
        field: 'justificativa',
        header: 'Justificativa',
        sortable: true,
        body: ({ justificativa }) => getValue(justificativa),
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          const getTooltip = (edition = false) => {
            if (this.state.hasRequisicaoModificacao) {
              return `${edition ? 'Edição' : 'Remoção'} desabilitada pois há uma Requisição de Modificação pendente`;
            }

            if (this.checkRequisicaoModificacao(rowData.dataCadastro, true)) {
              return `Criar Requisição de ${edition ? 'Modificação' : 'Remoção'}`;
            }

            return 'Editar';
          };

          const disabledEditionAndRemoval =
            (rowData?.definidoSistema && rowData?.statusObra === 'ATRASADA') || this.state.hasRequisicaoModificacao;

          return (
            <div className="actions p-d-flex p-jc-end">
              {rowData?.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Ver Requisição de Modificação"
                    className="p-button-sm p-button-info p-mr-1"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                          ':id',
                          rowData.idRequisicaoModificacao
                        )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <div className="flex">
                  <PermissionProxy resourcePermissions={this.getReadPermission()}>
                    <FcButton
                      icon="pi pi-eye"
                      className="p-button-sm p-button-primary p-mr-2"
                      tooltip="Detalhes"
                      onClick={() => this.props.onDetail(rowData)}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getReadPermission()}>
                    <FcButton
                      icon="pi pi-pencil"
                      className="p-button-sm p-button-success p-mr-2"
                      tooltip={getTooltip(true)}
                      onClick={() =>
                        this.pushUrlToHistory(
                          UrlRouter.obra.acompanhamento.situacaoObra.editar
                            .replace(':idObra', this.idObra)
                            .replace(':id', rowData.id)
                        )
                      }
                      disabled={disabledEditionAndRemoval}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-trash"
                      className="p-button-sm p-button-danger"
                      tooltip={getTooltip()}
                      onClick={() => {
                        this.setState({ selectedRow: rowData, showRequisicaoRemocao: true });
                      }}
                      disabled={disabledEditionAndRemoval}
                    />
                  </PermissionProxy>
                </div>
              )}
            </div>
          );
        },
      },
    ];

    const now = new Date();
    const { listKey, loading, ultimaSituacaoObra } = this.store;
    const { getDefaultTableProps } = this;

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getReadPermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() =>
              this.pushUrlToHistory(UrlRouter.obra.acompanhamento.situacaoObra.novo.replace(':idObra', this.idObra))
            }
            disabled={
              moment(this.store.ultimaSituacaoObra?.dataCadastro).isSame(moment(), 'month') ||
              !this.store.ultimaSituacaoObra?.dataCadastro ||
              this.hasObraFinalizada() ||
              this.state.hasRequisicaoModificacao
            }
          />
          {ultimaSituacaoObra?.dataInicioParalisacao &&
            this._renderContagemParalisacao(now, ultimaSituacaoObra?.dataInicioParalisacao)}
        </PermissionProxy>
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <div>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['dataCadastroFormatada', 'justificativa']}
            onSearch={() => this.store.recuperarUltimaSituacaoObra(this.idObra)}
            useOr
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this._renderDialogRequisicaoRemocao()}
        </div>
      </PermissionProxy>
    );
  }
}

SituacaoObraListagemPage.displayName = 'SituacaoObraListagemPage';

SituacaoObraListagemPage.propTypes = {
  history: PropTypes.any,
  match: PropTypes.any,
};

export default SituacaoObraListagemPage;
