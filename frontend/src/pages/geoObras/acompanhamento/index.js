import AdvancedSearch from 'fc/components/AdvancedSearch';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getValueElipsis, getValueMoney } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import AcompanhamentoIndexStore from '~/stores/geoObras/acompanhamento/indexStore';
import CicloObra from './cicloObra';
import DiarioObraIndexPage from './diarioObra';
import FormEntrega from './formEntrega/form';
import FormInicio from './formInicio/formInicio';
import Opcoes from './opcoes';
import UrlRouter from '~/constants/UrlRouter';

@observer
class AcompanhamentoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.acompanhamento);
    this.store = new AcompanhamentoIndexStore(this.props.match?.params.id);
    this.state = {
      inicioDialogVisible: false,
      entregaDialogVisible: false,
      diarioDialogVisible: false,
    };
  }

  getOpcoes = (obra) => {
    const { status, fase } = obra;
    const itens = [];

    if (obra.idRequisicaoModificacao) {
      if (fase !== 'CADASTRAL') {
        itens.push({
          label: 'Ver Requisição de Modificação',
          icon: 'pi pi-exclamation-triangle',
          command: () =>
            this.pushUrlToHistory(
              UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', obra.idRequisicaoModificacao)
            ),
        });
      }
    }

    if (fase !== 'CADASTRAL') {
      itens.push({
        label: 'Visualizar',
        icon: 'pi pi-image',
        command: () => this.toggleVisualizarObra(obra.id),
      });
    }

    if (!obra.idRequisicaoModificacao) {
      itens.push({
        label: status === 'NAO_INICIADA' ? 'Iniciar' : 'Modificar Inicio',
        icon: 'pi pi-arrow-up-right',
        disabled: obra.hasRequisicaoModificacao,
        command: () => this.toggleInicioDialog(obra),
      });
    }
    if (fase !== 'CADASTRAL' && status !== 'NAO_INICIADA') {
      itens.push(
        {
          label: 'Situações',
          icon: 'pi pi-cog',
          command: () =>
            this.pushUrlToHistory(UrlRouter.obra.acompanhamento.situacaoObra.index.replace(':idObra', obra.id)),
        },
        {
          label: 'Medições',
          icon: 'pi pi-percentage',
          command: () =>
            this.pushUrlToHistory(UrlRouter.obra.acompanhamento.medicoes.index.replace(':idObra', obra.id)),
        },
        {
          label: 'Diários',
          icon: 'pi pi-calendar-plus',
          command: () => this.toggleDiariosDialog(obra),
        }
      );
    }

    if (
      ['ENTREGA', 'FINALIZACAO'].includes(fase) &&
      obra.situacaoObra?.statusObra === 'CONCLUIDA' &&
      obra.situacaoObra?.motivo === 'RECEBIDA_DEFINITIVO' &&
      !obra.idRequisicaoModificacao
    ) {
      itens.push({
        label: fase === 'ENTREGA' ? 'Modificar Entrega' : 'Entregar',
        icon: 'pi pi-briefcase',
        disabled: obra.hasRequisicaoModificacao,
        command: () => this.toggleEntregaDialog(obra),
      });
    }

    return itens;
  };

  toggleVisualizarObra(obraId) {
    localStorage.setItem('selectedObraId', obraId);
    this.pushUrlToHistory(UrlRouter.obra.mapa.index);
  }

  toggleInicioDialog({ id }) {
    this.store.storeObraInicio.initializeArquivos(id, () => {
      this.store.storeObraInicio.initialize(id, {}, () => {
        const idObra = this.store.storeObraInicio.loadedObject.id;
        const contrato = this.store.storeObraInicio.loadedObject.contrato;
        if (idObra) {
          this.store.storeMedicaoIndex.setIdObra(idObra);

          this.store.storeMedicaoIndex.carregarPorObra(idObra, () => {
            this.store.storeObraInicio.updateMinDateInicio(contrato ? contrato.dataVigenciaInicial : null);
            this.store.storeObraInicio.updateMaxDateInicio(
              contrato ? contrato.dataFinalVigente ?? contrato.dataVigenciaFinal : null
            );
          });
        }

        this.setState({ inicioDialogVisible: true });
      });
    });
  }

  toggleEntregaDialog(obra) {
    this.store.storeObraEntrega.initializeArquivos(obra.id, () =>
      this.store.storeObraEntrega.initialize(obra.id, obra, () => {
        this.setState({ entregaDialogVisible: !this.state.entregaDialogVisible });
      })
    );
  }

  toggleDiariosDialog({ id }) {
    this.store.storeObra.initialize(id, {}, () => {
      this.setState({
        diarioDialogVisible: !this.state.diarioDialogVisible,
      });
    });
  }

  onSuccessDialogInicio() {
    this.setState({ inicioDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogEntrega() {
    this.setState({ entregaDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogMedicao() {
    this.setState({ medicoesDialogVisible: false }, () => this.store.load());
  }

  renderDialogInicio() {
    const onHide = () => this.setState({ inicioDialogVisible: false });

    return (
      <Dialog
        header="Cadastro da Fase Inicial"
        visible={this.state.inicioDialogVisible}
        style={{ width: '60vw' }}
        onHide={onHide}
      >
        <FormInicio
          onSuccess={() => this.onSuccessDialogInicio()}
          onCancel={onHide}
          store={this.store.storeObraInicio}
        />
      </Dialog>
    );
  }

  renderDialogEntrega() {
    const onHide = () => this.setState({ entregaDialogVisible: false });
    return (
      <Dialog
        header="Cadastro de Entrega da Obra"
        visible={this.state.entregaDialogVisible}
        style={{ width: '60vw' }}
        onHide={onHide}
      >
        <FormEntrega
          onSuccess={() => this.onSuccessDialogEntrega()}
          onCancel={onHide}
          store={this.store.storeObraEntrega}
        />
      </Dialog>
    );
  }

  renderDiarioDialog() {
    let content = <></>;
    const onHide = () => this.setState({ diarioDialogVisible: false });
    if (this.store?.storeObra?.object?.id) {
      content = (
        <Dialog
          header="Diários da Obra"
          visible={this.state.diarioDialogVisible}
          style={{ width: '50vw' }}
          onHide={onHide}
        >
          <DiarioObraIndexPage obraId={this.store?.storeObra?.object?.id} history={this.props.history} />
        </Dialog>
      );
    }
    return content;
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'N° da Obra/Contrato',
        sortable: true,
        body: ({ numeroFormatado, contrato, administracaoDireta }) =>
          administracaoDireta ? numeroFormatado : contrato.numeroContrato,
        style: { width: '10%' },
      },
      {
        field: 'tipo',
        header: 'Origem',
        body: ({ administracaoDireta }) => (
          <span className={`base-badge base-${administracaoDireta ? 'green' : 'blue'}`}>
            {administracaoDireta ? 'ADMINISTRAÇÃO DIRETA' : 'CONTRATO'}
          </span>
        ),
        sortable: true,
      },
      {
        field: 'descricao',
        header: 'Descrição',
        style: { width: '20%' },
        body: ({ descricao }) => getValueElipsis(descricao, 100),
      },
      {
        field: 'valor',
        header: 'Valor da Obra/Contrato',
        body: ({ valor, contrato, administracaoDireta }) =>
          getValueMoney(administracaoDireta ? valor : contrato.aditivo ? contrato.aditivo : contrato.valorGlobal),
        sortable: true,
        style: { width: '15%' },
      },
      {
        field: 'complexoObra',
        header: 'Complexo de Obra',
        body: ({ nomeComplexoObra }) => getValueElipsis(nomeComplexoObra),
        sortable: true,
      },
      {
        field: 'ciclo',
        header: 'Ciclo de Vida',
        style: { width: '45%' },
        body: (obra) => (
          <CicloObra
            fase={obra.fase}
            status={obra.status}
            situacaoStatus={obra.situacaoObra?.statusObra}
            situacaoMotivo={obra.situacaoObra?.motivo}
          />
        ),
      },
      {
        style: { width: '10px' },
        body: (obra) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <Opcoes itens={this.getOpcoes(obra)} />
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;
    const breacrumbItems = [{ label: 'Acompanhamento' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroContrato', 'numeroFormatado', 'descricao', 'nomeComplexoObra']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this.renderDialogInicio()}
          {this.renderDialogEntrega()}
          {this.renderDiarioDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

AcompanhamentoIndexPage.displayName = 'AcompanhamentoIndexPage';

export default AcompanhamentoIndexPage;
