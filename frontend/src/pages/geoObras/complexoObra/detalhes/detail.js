import React from 'react';
import PropTypes from 'prop-types';
import { getValue } from 'fc/utils/utils';
import { Link } from 'react-router-dom';
const { observer } = require('mobx-react');
import classNames from 'classnames';
import AppStore from 'fc/stores/AppStore';
import { Divider } from 'primereact/divider';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ListagemObraIndex from './listagemObras';

@observer
class ComplexoObraDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.isDialog = this.props.isDialog;
    this.state = {
      visibleDialogObras: false,
      showDetalhesDialog: false,
      activeTabIndex: 0,
    };
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderTabs(complexoObra) {
    const tabs = [];
    tabs.push({ id: 0, header: 'Obras Associadas', content: this._renderTabObras(complexoObra.id) });

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderTabObras(idComplexoObra) {
    return (
      <div className="p-fluid p-formgrid">
        <ListagemObraIndex idComplexoObra={idComplexoObra} isDialog={true} />
      </div>
    );
  }

  renderPanelDadosComplexoObra = (complexoObra) => {
    return (
      <div className="p-fluid p-formgrid">
        <div className="p-d-flex p-jc-between p-ai-center p-mb-3">
          {!this.isDialog && this._renderDivider('Detalhes', `ComplexoObra-${getValue(complexoObra.nome)}`)}
        </div>
        {this._renderValue('Nome', getValue(complexoObra.nome))}
        {this._renderValue('Descrição', getValue(complexoObra.descricao))}
      </div>
    );
  };

  _renderDivider(label) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  render() {
    const { complexoObraStore } = this.props;
    const complexoObra = complexoObraStore.object;
    return (
      <div>
        {complexoObra && (
          <>
            {this.renderPanelDadosComplexoObra(complexoObra)}
            <Divider style={{ marginBottom: `1px` }} />
            {this._renderTabs(complexoObra)}
          </>
        )}
      </div>
    );
  }
}

ComplexoObraDetailPage.propTypes = {
  complexoObraStore: PropTypes.any,
  isDialog: PropTypes.bool,
};

export default ComplexoObraDetailPage;
