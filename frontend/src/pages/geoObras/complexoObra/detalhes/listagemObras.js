import React from 'react';
import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import {
  checkuserContextIsAuditor,
  checkUserContextIsJurisdicionado,
  getValueByKey,
  getValueDate,
  getValueElipsis,
  getValueMoney,
} from 'fc/utils/utils';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import ObraIndexStore from '~/stores/geoObras/obra/indexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import UrlRouter from '~/constants/UrlRouter';

@observer
class ListagemObraIndex extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.complexoObra);
    this.store = new ObraIndexStore(null, this.props.idComplexoObra);
    this.isDialog = this.props.isDialog;
  }

  render() {
    const userIsAuditor = checkuserContextIsAuditor();
    const complexoObraObject = !this.isDialog
      ? {
          field: 'complexoObra',
          header: 'Complexo de Obra',
          body: ({ complexoObra }) => getValueElipsis(complexoObra.nome),
          sortable: true,
        }
      : {};

    const fornecedorContratoObject =
      checkUserContextIsJurisdicionado() || userIsAuditor
        ? {
            field: 'contrato',
            header: 'Fornecedor do Contrato',
            body: ({ contrato }) => getValueElipsis(contrato?.licitante?.nome, 150),
            sortable: true,
          }
        : {};

    const orgaoJuridiscionadoOrigemObject = userIsAuditor
      ? {
          field: 'entidade',
          header: 'Órgão Jurisdicionado de Origem',
          body: ({ entidade }) => getValueElipsis(entidade.nome, 150),
          sortable: true,
        }
      : {};
    const columns = [
      {
        field: 'numero',
        header: 'N° da Obra/Contrato',
        sortable: true,
        body: ({ numeroFormatado, contrato, administracaoDireta }) =>
          administracaoDireta ? numeroFormatado : contrato.numeroContrato,
      },
      {
        field: 'origem',
        header: 'Origem',
        body: ({ administracaoDireta }) => (
          <span className={`base-badge base-${administracaoDireta ? 'green' : 'blue'}`}>
            {administracaoDireta ? 'ADMINISTRAÇÃO DIRETA' : 'CONTRATO'}
          </span>
        ),
        sortable: true,
      },
      fornecedorContratoObject,
      orgaoJuridiscionadoOrigemObject,
      {
        style: { width: '80%' },
        field: 'descricao',
        header: 'Descrição',
        body: ({ descricao }) => getValueElipsis(descricao, 150),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor da Obra/Contrato',
        body: ({ valor, contrato, administracaoDireta }) =>
          getValueMoney(administracaoDireta ? valor : contrato.aditivo ? contrato.aditivo : contrato.valorGlobal),
        sortable: true,
      },
      {
        field: 'dataPrevistaInicio',
        header: 'Início',
        body: ({ dataPrevistaInicio }) => getValueDate(dataPrevistaInicio),
        sortable: true,
      },
      {
        field: 'dataPrevistaConclusao',
        header: 'Fim',
        body: ({ dataPrevistaConclusao }) => getValueDate(dataPrevistaConclusao),
        sortable: true,
      },
      {
        field: 'statusObra',
        header: 'Situação',
        body: ({ statusObra }) => getValueByKey(statusObra, DadosEstaticosService.getStatusObra()),
        sortable: true,
      },
      complexoObraObject,
      {
        style: { width: '40px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <Link target="_blank" rel="noreferrer" to={UrlRouter.obra.cadastro.listagem.replace(':id', rowData.id)}>
                <FcButton icon="pi pi-building" className="p-button-sm p-mr-2" tooltip="Ver Obra" />
              </Link>
            </div>
          );
        },
      },
      {
        style: { width: '40px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <Link
                target="_blank"
                rel="noreferrer"
                to={UrlRouter.obra.acompanhamento.listagem.replace(':id', rowData.id)}
              >
                <FcButton
                  icon="pi pi-calendar-times"
                  className="p-button-sm p-mr-2"
                  tooltip="Ver Acompanhamento da Obra"
                />
              </Link>
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <>
        <AdvancedSearch
          searchFields={['numeroContrato', 'numeroFormatado', 'descricao']}
          alwaysDrawer
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <IndexDataTable
          disableColumnToggle
          columns={columns}
          value={listKey}
          loading={loading}
          {...getDefaultTableProps()}
        />
      </>
    );
  }
}

export default ListagemObraIndex;
