import { observer } from 'mobx-react';
import { ProgressSpinner } from 'primereact/progressspinner';
import React from 'react';
import PropTypes from 'prop-types';
import ComplexoObraFormStore from '~/stores/geoObras/complexoObra/formStore';
import ComplexoObraDetailPage from './detail';

@observer
class ComplexoObraIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.complexoObraStore = new ComplexoObraFormStore();
    this.isDialog = this.props.isDialog;
  }

  componentDidMount() {
    const { id } = this.props;
    this.complexoObraStore.initialize(id, {}, () => {});
  }

  render() {
    const complexoObra = this.complexoObraStore?.object;
    let content = <></>;
    if (this.complexoObraStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (complexoObra) {
      content = <ComplexoObraDetailPage complexoObraStore={this.complexoObraStore} isDialog={this.isDialog} />;
    } else {
      content = <div>Erro ao exibir detalhes do Complexo de Obras.</div>;
    }

    return content;
  }
}

ComplexoObraIndexDetailPage.propTypes = {
  id: PropTypes.number,
  isDialog: PropTypes.bool,
};

export default ComplexoObraIndexDetailPage;
