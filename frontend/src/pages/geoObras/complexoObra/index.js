import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ComplexoObraListagemPage from './listagem';
import ComplexoObraIndexDetailPage from './detalhes/indexDetail';
import ComplexoObrasIndexStore from '~/stores/geoObras/complexoObra/indexStore';

@observer
class ComplexoObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.complexoObra);
    this.store = new ComplexoObrasIndexStore();
    this.state = {
      data: [
        {
          id: 0,
          header: 'Listagem',
          closeable: false,
          content: (
            <ComplexoObraListagemPage
              {...props}
              onDetail={(complexoObra) => this.onDetail(complexoObra)}
              pushUrlToHistory={(url) => this.pushUrlToHistory(url)}
            />
          ),
        },
      ],
      count: 0,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  onDetail(complexoObra) {
    const existingTab = this.state.data.find((tab) => tab.idComplexoObra === complexoObra.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newComplexoObra = {
        id: this.state.data.length,
        idComplexoObra: complexoObra.id,
        header: complexoObra.nome,
        closeable: true,
        content: <ComplexoObraIndexDetailPage id={complexoObra.id} />,
      };
      this.setState({ data: [...this.state.data, newComplexoObra], count: this.state.count + 1 });
    }
  }

  render() {
    const breacrumbItems = [{ label: 'Cadastro de Complexo de Obras' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

ComplexoObraIndexPage.displayName = 'ComplexoObraIndexPage';

export default ComplexoObraIndexPage;
