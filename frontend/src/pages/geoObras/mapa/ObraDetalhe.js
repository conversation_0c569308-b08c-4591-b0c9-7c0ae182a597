import classNames from 'classnames';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import CardList from 'fc/components/CardList';
import Carousel from 'fc/components/Carousel';
import ModalImages from 'fc/components/Carousel/ModalImages';
import FcButton from 'fc/components/FcButton';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import AppStore from 'fc/stores/AppStore';
import { DATE_FORMAT } from 'fc/utils/date';
import {
  getValue,
  getValueDate,
  getValueMoney,
  getValueByKey,
  formatCep,
  generateFullURL,
  checkBoolean,
} from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import { Fieldset } from 'primereact/fieldset';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { useEffect, useState } from 'react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';

const ObraDetalhe = observer((props) => {
  const { idObra, onHide, obraStore, medicaoIndexStore, medicaoFormStore, googleStreetViewUrl } = props;
  const [obraDetalheVisible, setObraDetalheVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const [fotosInicio, setFotosInicio] = useState([]);
  const [fotosFinal, setFotosFinal] = useState([]);
  const [fotosMedicao, setFotosMedicao] = useState([]);

  const [modalFotosMedicaoVisible, setModalFotosMedicaoVisible] = useState(false);
  const [modalArquivosMedicaoVisible, setModalArquivosMedicaoVisible] = useState(false);
  const [loadingMedicao, setLoadingMedicao] = useState({});

  const obra = obraStore.object;

  useEffect(() => {
    if (idObra) {
      obraStore.initialize(idObra, {}, () =>
        obraStore.initializeArquivos(idObra, () => {
          const filtro = {
            page: { index: 1, size: 999 },
            sort: {
              by: 'dataCadastro',
              order: 'asc',
            },
            andParameters: [
              {
                field: 'obra',
                operator: SearchOperators.EQUAL_TO.value,
                value: idObra,
              },
            ],
          };
          medicaoIndexStore.load(filtro);
          obraStore
            .downloadFotos(obraStore.arquivos?.filter((f) => f.fase === 'INICIAL' && f.tipo === 'FOTO'))
            .then((images) => setFotosInicio(images));
          obraStore
            .downloadFotos(obraStore.arquivos?.filter((f) => f.fase === 'FINALIZACAO' && f.tipo === 'FOTO'))
            .then((images) => setFotosFinal(images));

          setObraDetalheVisible(true);
        })
      );
    } else {
      setObraDetalheVisible(false);
    }
  }, [idObra]);

  const getValueStyle = () => {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  };

  const getValueLayoutClassNames = () => {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  };

  const renderValue = (label, value, col = 12, type = 'value', url = '#') => {
    return (
      <div
        style={getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-4 lg:col-4 md:col-2 sm:col-2 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-8 lg:col-8 md:col-10 sm:col-10 details-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div className="flex flex-column">
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <a href={url} target="_blank" rel="noopener noreferrer" style={{ color: '#3F51B5' }}>
                <u>{value}</u>
              </a>
            }
          </div>
        )}
      </div>
    );
  };

  const renderDivider = (label) => {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  };

  const renderPanelDadosObra = (obra) => {
    let linkContrato = null;

    if (!obra.administracaoDireta) {
      linkContrato = {
        link: generateFullURL(UrlRouter.cadastrosConsulta.contrato.detalhe.replace(':id', obra.contrato.id)),
        text: `Contrato ${obra.contrato.numero + '/' + obra.contrato.anoContrato ?? ''}`,
        permission: AccessPermission.contrato.readPermission,
      };
    }

    return (
      <div className="p-fluid p-formgrid">
        {renderValue(
          'Número da Obra/Contrato',
          getValue(obra.administracaoDireta ? obra.numeroFormatado : obra.contrato.numeroContrato)
        )}
        {renderValue('Data de Cadastro', getValueDate(obra.dataCadastro, DATE_FORMAT))}
        {renderValue(
          'Valor da Obra/Contrato',
          getValueMoney(
            obra.administracaoDireta
              ? obra.valor
              : obra.contrato.aditivo
              ? obra.contrato.aditivo
              : obra.contrato.valorGlobal
          )
        )}
        {renderValue(
          'Endereços',
          getValue(obra.enderecos?.map((e) => `${e.endereco} | ${formatCep(e.cep)}`)),
          12,
          'list'
        )}
        {renderValue('Street View ', 'Google Maps', 12, 'link', googleStreetViewUrl)}
        {renderValue('Descrição', obra.descricao)}
        {obra.administracaoDireta
          ? renderValue('Origem', 'ADMINISTRAÇÃO DIRETA')
          : renderValue('Origem', linkContrato.text, 12, 'link', linkContrato.link)}
        {renderValue('Status', getValueByKey(obra.statusObra, DadosEstaticosService.getStatusObra()))}
        {!obra.administracaoDireta && (
          <>
            <div id={`gestoresFiscais-${obra.contrato.id}`}>{renderDivider('Gestores e Fiscais do Contrato')}</div>
            {renderValue('Gestor', getValue(obra.contrato?.gestorContrato))}
            {renderValue('Gestor Substituto', getValue(obra.contrato?.gestorSuplente))}
            {renderValue('Fiscal', getValue(obra.contrato?.fiscalTitular))}
            {renderValue('Fiscal Substituto', getValue(obra.contrato?.fiscalSuplente))}
          </>
        )}
        <Fieldset legend="Arquivos do Cadastro" className="p-col-12">
          <MultipleFileUploader
            isObra
            downloadOnly
            store={obraStore.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoObra()}
            showFileType
            filterTypes={{
              filter: {
                column: 'fase',
                values: ['CADASTRAL'],
              },
            }}
          />
        </Fieldset>
      </div>
    );
  };

  const carregaFotosMedicao = (obra, idMedicao, callback) => {
    const newLoadingMedicao = { ...loadingMedicao };
    newLoadingMedicao[idMedicao] = true;
    setLoadingMedicao(newLoadingMedicao);
    medicaoFormStore.initializeArquivos(obra, idMedicao, () => {
      const fotos = medicaoFormStore.arquivoMedicaoFotosList?.filter((arquivo) => arquivo.tipo === 'FOTO');
      medicaoFormStore
        .downloadFotos(fotos)
        .then((images) => {
          callback && callback(images);
        })
        .finally(() => {
          const newLoadingMedicao = { ...loadingMedicao };
          newLoadingMedicao[idMedicao] = false;
          setLoadingMedicao(newLoadingMedicao);
        });
    });
  };

  const carregaArquivosMedicao = (obra, idMedicao, callback) => {
    const newLoadingMedicao = { ...loadingMedicao };
    newLoadingMedicao[idMedicao] = true;
    setLoadingMedicao(newLoadingMedicao);
    medicaoFormStore.initializeArquivos(obra, idMedicao, () => {
      callback && callback();
      const newLoadingMedicao = { ...loadingMedicao };
      newLoadingMedicao[idMedicao] = false;
      setLoadingMedicao(newLoadingMedicao);
    });
  };

  const getCardEllipsisOptions = (obra, medicao) => {
    const items = [
      {
        label: 'Fotos',
        icon: 'pi pi-images',
        command: () =>
          carregaFotosMedicao(obra, medicao.id, (images) => {
            setFotosMedicao(images);
            setModalFotosMedicaoVisible(true);
          }),
      },
      {
        label: 'Documentos',
        icon: 'pi pi-file',
        command: () => carregaArquivosMedicao(obra, medicao.id, () => setModalArquivosMedicaoVisible(true)),
      },
    ];

    return items;
  };

  const renderMedicoesListInstance = () => {
    const fields = [
      {
        label: 'Medição',
        field: 'dataCadastro',
        value: 'title',
        body: ({ dataCadastro }) => `Medição - ${getValueDate(dataCadastro, DATE_FORMAT)}`,
      },
      {
        label: 'Número Empenho',
        field: 'numeroEmpenho',
        value: 'subtitle',
        body: ({ empenho }) => `Empenho Nº: ${getValue(empenho?.numeroEmpenho)}`,
      },

      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        body: ({ id }) =>
          loadingMedicao[id] ? (
            <div className="p-d-inline p-d-flex align-items-center">
              <ProgressSpinner style={{ width: '50px', height: 'auto' }} />
            </div>
          ) : (
            ''
          ),
      },
      {
        field: 'intervalo',
        label: 'Intervalo da Medição',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-calendar',
        body: ({ dataInicio, dataFim }) =>
          `${getValueDate(dataInicio, DATE_FORMAT)} a ${getValueDate(dataFim, DATE_FORMAT)}`,
      },
      {
        field: 'valor',
        label: 'Valor da Medição',
        value: 'iconLabel',
        color: '#d080fa',
        icon: 'pi pi-money-bill',
        body: ({ valor }) => getValueMoney(valor),
      },
      {
        field: 'percentual',
        label: 'Percentual de Conclusão',
        value: 'iconLabel',
        color: '#46ad38',
        icon: 'pi pi-hourglass',
        body: ({ percentualConclusao }) => `${getValueMoney(percentualConclusao)} %`,
      },
      {
        field: 'valorContratoAlterado',
        label: 'Existe Alteração no Valor do Contrato',
        value: 'iconLabel',
        color: '#eead2d',
        icon: 'pi pi-briefcase',
        body: ({ valorContratoAlterado }) => checkBoolean(valorContratoAlterado),
      },
      {
        field: 'projetoAlterado',
        label: 'Existe Alteração no Projeto',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-building',
        body: ({ projetoAlterado }) => checkBoolean(projetoAlterado),
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (medicao) => getCardEllipsisOptions(obra, medicao),
      },
    ];
    return (
      <div>
        <CardList
          fields={fields}
          store={medicaoIndexStore}
          emptyMessage="Nenhuma medição cadastrada para a obra."
          footer={() => ''}
        />
      </div>
    );
  };

  const renderPanelCicloVida = (obra) => {
    return (
      <>
        <div className="p-fluid p-formgrid">
          {obra.dataInicio && (
            <>
              {renderDivider('Fase Inicial')}
              {renderValue('Data de Início', getValueDate(obra.dataInicio, DATE_FORMAT))}
              <Carousel images={fotosInicio} />
            </>
          )}

          {renderDivider('Fase Medição')}
          {renderMedicoesListInstance()}

          {obra.dataFim && (
            <>
              {renderDivider('Fase Final')}
              {renderValue('Data Final', getValueDate(obra.dataFim, DATE_FORMAT))}
              <Carousel images={fotosFinal} />
            </>
          )}
          {obra.dataConclusao && (
            <>
              {renderDivider('Fase Entrega')}
              {renderValue('Data Conclusão', getValueDate(obra.dataConclusao, DATE_FORMAT))}
              {renderValue('Data Recebimento', getValueDate(obra.dataRecebimento, DATE_FORMAT))}
              {renderValue(
                'Tipo Encerramento',
                getValueByKey(obra.tipoEncerramento, DadosEstaticosService.getTipoEncerramentoObra())
              )}
              <Fieldset legend="Arquivos da Entrega" className="p-col-12">
                <MultipleFileUploader
                  isObra
                  downloadOnly
                  store={obraStore.fileStore}
                  fileTypes={DadosEstaticosService.getTipoArquivoObra()}
                  showFileType
                  filterTypes={{
                    filter: {
                      column: 'fase',
                      values: ['ENTREGA'],
                    },
                  }}
                />
              </Fieldset>
            </>
          )}
        </div>
        <ModalImages
          images={fotosMedicao}
          visible={modalFotosMedicaoVisible}
          onHide={() => setModalFotosMedicaoVisible(false)}
        />
        <Dialog
          header="Arquivo da Medição"
          style={{ width: '50vw' }}
          draggable={false}
          visible={modalArquivosMedicaoVisible}
          onHide={() => setModalArquivosMedicaoVisible(false)}
        >
          <MultipleFileUploader
            isObra
            downloadOnly
            store={medicaoFormStore.fileStoreDocumentos}
            fileTypes={DadosEstaticosService.getTipoArquivoMedicaoObra()}
            showFileType
          />
        </Dialog>
      </>
    );
  };

  return (
    <Sidebar
      position="right"
      dismissable={false}
      modal={false}
      id="side-bar-obras"
      style={{
        width: '530px',
        display: 'flex',
        flexDirection: 'column',
        paddingTop: '1rem',
      }}
      visible={obraDetalheVisible}
    >
      <div id="header" className="flex justify-content-between align-items-center mt-2 text-xl font-bold">
        <div className="flex gap-2 align-items-center">
          <span>Detalhes da Obra</span>
        </div>
        <FcButton
          icon="pi pi-times"
          className="p-button-rounded p-button-text"
          onClick={() => onHide()}
          style={{ color: 'black' }}
        />
      </div>
      <Divider className="mt-3" />
      {obraStore.loading ? (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      ) : (
        <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
          <TabPanel header="Dados da Obra">{obra && renderPanelDadosObra(obra)}</TabPanel>
          <TabPanel header="Ciclo de Vida">{obra && renderPanelCicloVida(obra)}</TabPanel>
        </TabView>
      )}
    </Sidebar>
  );
});

export default ObraDetalhe;
