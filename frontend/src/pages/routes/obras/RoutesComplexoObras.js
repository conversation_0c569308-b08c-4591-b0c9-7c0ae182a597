import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import NotFound from 'fc/pages/NotFound';
import ComplexoObraIndexPage from '~/pages/geoObras/complexoObra';
import EditComplexoObra from '~/pages/geoObras/complexoObra/edit';
import NewComplexoObra from '~/pages/geoObras/complexoObra/new';

const RoutesComplexoObras = () => {
  return (
    <Switch>
      <Route path={UrlRouter.obra.complexoObra.index} exact component={ComplexoObraIndexPage} />
      <Route path={UrlRouter.obra.complexoObra.novo} exact component={NewComplexoObra} />
      <Route path={UrlRouter.obra.complexoObra.editar} exact component={EditComplexoObra} />
      <Route component={NotFound} />
    </Switch>
  );
};
export default RoutesComplexoObras;
