import React, { useState, useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import classNames from 'classnames';
import { getLightenColor, getNumberUnitThousands, getValue, getValueMoney, isValueValid } from 'fc/utils/utils';
import PropTypes from 'prop-types';
import Panel from './panel';
import FcButton from 'fc/components/FcButton';
import InputMonetary from 'fc/components/InputMonetary';
import Resumo from './resumo';
import DialogDetalhes from '~/pages/vencedores/detalhes';
import Tooltip from 'fc/components/Tooltip';
import VirtualizedPickList from 'fc/components/VirtualizedPickList';

import './style.scss';

const MemoizedItemTemplate = React.memo(({ store, item, vencedor }) => {
  if (!item) return null;

  const quantidadeDisponivel = item.quantidade;
  const quantidadeMaxima = vencedor ? store.getQuantidadeFifty(vencedor.quantidade, item.itemLote.fracionario) : 0;

  return (
    <div className="flex flex-wrap p-2 align-items-center gap-3">
      <div className="flex-1 flex flex-column gap-2">
        <span className="font-bold">
          {item.itemLote.numero ? `${item.itemLote.numero} - ` : ''}
          {getValue(item.itemLote.materialDetalhamento?.pdm?.nome)}
        </span>
        <div className="flex gap-2">
          <Tag
            value={item.lote.nome}
            icon="pi pi-box"
            style={{ backgroundColor: '#fbf7ff', color: '#8f48d2', border: '1px solid #8f48d2' }}
          />
          <Tag
            value={`Quantidade: ${getNumberUnitThousands(quantidadeDisponivel)}/${getNumberUnitThousands(
              quantidadeMaxima
            )}`}
            style={{
              backgroundColor: getLightenColor('#2F83DC', 0.7),
              color: '#2F83DC',
              border: '1px solid #2F83DC',
            }}
          />
        </div>
      </div>
    </div>
  );
});

const ItensCarona = observer((props) => {
  const { store, readOnly, showDesconto, decimalPlaces } = props;
  const [editing, setEditing] = useState({});
  const [itemError, setItemError] = useState({});
  const [collapsed, setCollapsed] = useState({});
  const [visible, setVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const getId = useCallback((item) => `item-${item?.itemLote?.id}-licitante-${item?.licitante?.id}`, []);

  const optionsLotesByVencedor = useMemo(() => {
    if (!store.vencedor?.itens) return [];

    const lotesUnicos = new Map();
    store.vencedor.itens.forEach((i) => {
      if (!lotesUnicos.has(i.lote.id)) {
        lotesUnicos.set(i.lote.id, { ...i.lote });
      }
    });
    return Array.from(lotesUnicos.values());
  }, [store.vencedor]);

  const detentorItemIdsSet = useMemo(() => {
    return new Set(store.detentores?.map((it) => it.itemLote.id) || []);
  }, [store.detentores]);

  const sourceOptions = useMemo(() => {
    if (!store.vencedor?.itens) return [];

    return store.vencedor.itens
      .filter((i) => !detentorItemIdsSet.has(i.itemLote.id) && (!store.lote || i.lote.id === store.lote.id))
      .map((i) => ({ ...i, quantidade: store.getQuantidadeFifty(i.quantidade, i.itemLote.fracionario) }));
  }, [store.vencedor, store.lote, detentorItemIdsSet, store]);

  const targetOptions = useMemo(() => {
    if (!store.vencedor) return [];

    return store.detentores.filter((v) => v.licitante.id === store.vencedor.licitante.id);
  }, [store.detentores, store.vencedor]);

  const vencedoresMap = useMemo(() => {
    if (!store.vencedores) return new Map();
    return new Map(store.vencedores.map((v) => [`${v.itemLote.id}-${v.licitante.id}`, v]));
  }, [store.vencedores]);

  const itemTemplate = useCallback(
    (item) => {
      const key = `${item.itemLote.id}-${item.licitante.id}`;
      const vencedorLicitacao = vencedoresMap.get(key);
      return <MemoizedItemTemplate store={store} item={item} vencedor={vencedorLicitacao} />;
    },
    [vencedoresMap]
  );

  const handleVencedorChange = useCallback((e) => store.setVencedor(e.value), [store]);
  const handleLoteChange = useCallback((e) => store.setLote(e.value), [store]);
  const handlePickListChange = useCallback((e) => store.onChangePickList(e), [store]);
  const toggleLote = useCallback((id) => {
    setCollapsed((prev) => ({ ...prev, [id]: !prev[id] }));
  }, []);

  const openDetalhesDialog = useCallback((item) => {
    setSelectedItem(item);
    setVisible(true);
  }, []);

  const handleRemoveItem = useCallback((item) => store.removeItemVencedor(item), [store]);

  const _validateRequiredFields = useCallback(
    (item) => {
      const itemId = getId(item);
      const errors = {};
      let hasError = false;

      if (!isValueValid(item.quantidade) || item.quantidade <= 0) {
        errors.quantidade = true;
        hasError = true;
      }
      if (!isValueValid(item.valorUnitario) || item.valorUnitario <= 0) {
        errors.valorUnitario = true;
        hasError = true;
      }

      setItemError((prev) => ({ ...prev, [itemId]: errors }));

      if (hasError) {
        setEditing((prev) => ({ ...prev, [itemId]: { ...prev[itemId], ...errors } }));
      }

      return hasError;
    },
    [getId]
  );

  const handleConfirmClick = useCallback(
    (item) => {
      if (!_validateRequiredFields(item)) {
        const itemId = getId(item);
        setEditing((prev) => ({ ...prev, [itemId]: {} }));
        setItemError((prev) => ({ ...prev, [itemId]: false }));
        store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', true);
      }
    },
    [getId, store, _validateRequiredFields]
  );

  const handleEditClick = useCallback(
    (item) => {
      const itemId = getId(item);
      setEditing((prev) => ({ ...prev, [itemId]: {} }));
      store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', false);
    },
    [getId, store]
  );

  const enableEdit = useCallback(
    (item, field) => {
      if (!item.preenchido) {
        const itemId = getId(item);
        setEditing((prev) => ({ ...prev, [itemId]: { ...prev[itemId], [field]: true } }));
      }
    },
    [getId]
  );

  const columns = useMemo(
    () =>
      [
        {
          header: 'Detalhes',
          style: { width: showDesconto ? '10%' : '15%' },
          body: (item) =>
            `${item.lote?.nome ? item.lote.nome + ' - ' : ''} ${
              getValue(item.itemLote?.materialDetalhamento?.pdm?.nome) || ''
            } de código ${getValue(item.itemLote?.materialDetalhamento?.codigo) || ''}`,
        },
        {
          header: 'Marca/Modelo',
          style: { width: showDesconto ? '15%' : '20%' },
          body: (item) => <span className="text-disabled">{getValue(item.marcaModelo)}</span>,
        },
        {
          header: (
            <span>
              Quantidade<span className="p-error"> *</span>
            </span>
          ),
          style: { width: '15%' },
          body: (item) => {
            const itemId = getId(item);
            const itemErrorQuantidade = itemError[itemId]?.quantidade;
            return (editing[itemId]?.quantidade || item.preenchido) && !itemErrorQuantidade ? (
              <span
                className={classNames({ pointer: !item.preenchido })}
                onClick={() => enableEdit(item, 'quantidade')}
              >
                {getNumberUnitThousands(item.quantidade)}
              </span>
            ) : (
              <InputMonetary
                className={itemErrorQuantidade ? 'p-invalid p-error' : ''}
                value={item.quantidade}
                onChange={(e) => {
                  store.updateAttributeItem(item.itemLote.id, item.licitante.id, 'quantidade', e);
                }}
                decimalPlaces={item.itemLote.fracionario ? 2 : 0}
                min={0}
                onBlur={() => !_validateRequiredFields(item) && enableEdit(item, 'quantidade', false)}
              />
            );
          },
        },
        {
          header: 'Valor Negociado',
          style: { width: '15%' },
          body: (item) => <span className="text-disabled">{getValueMoney(item.valorUnitario)}</span>,
        },
        showDesconto && {
          header: 'Desconto(%)',
          body: (item) => {
            !editing[getId(item)]?.desconto && !item.preenchido ? (
              <InputMonetary
                onChange={(e) => store.setDescontoItem(item.itemLote?.id, item.licitante.id, e)}
                placeholder="Desconto"
                value={item.desconto}
                decimalPlaces={2}
                min={0}
                max={100}
                onKeyDown={({ key }) => handleKey(key, item, 'desconto')}
              />
            ) : (
              <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'desconto')}>
                {getNumberFractionDigits(item.desconto)}
              </span>
            );
          },
        },
        {
          header: 'Valor Total',
          style: { width: '10%' },
          body: (item) => <span className="text-disabled">{getValueMoney(item.valor)}</span>,
        },
        {
          header: 'Status',
          style: { width: '10%' },
          body: (item) => (
            <Tag
              severity={item.preenchido ? 'success' : 'danger'}
              value={item.preenchido ? 'Preenchido' : 'Pendente'}
              rounded
            />
          ),
        },
        {
          header: 'Ações',
          style: { width: '15%' },
          body: (item) => (
            <>
              {!item.preenchido ? (
                <FcButton
                  type="button"
                  icon="pi pi-check"
                  className="p-button-text"
                  onClick={() => handleConfirmClick(item)}
                />
              ) : (
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-text"
                  onClick={() => handleEditClick(item)}
                />
              )}
              <FcButton
                type="button"
                icon="pi pi-eye"
                className="p-button-text"
                onClick={() => openDetalhesDialog(item)}
              />
              <FcButton
                type="button"
                icon="pi pi-trash"
                className="p-button-text"
                onClick={() => handleRemoveItem(item)}
              />
            </>
          ),
        },
      ].filter(Boolean),
    [
      showDesconto,
      editing,
      itemError,
      getId,
      enableEdit,
      store,
      handleConfirmClick,
      handleEditClick,
      openDetalhesDialog,
      handleRemoveItem,
    ]
  );

  const _renderHeader = useCallback(
    (vencedor, className) => (
      <>
        <div className="flex justify-content-between align-items-center w-full">
          <div className="flex-left pointer" onClick={() => toggleLote(vencedor.licitante.id)}>
            <i
              className={classNames('p-m-2', {
                'pi pi-angle-right': collapsed[vencedor.licitante?.id],
                'pi pi-angle-down': !collapsed[vencedor.licitante?.id],
              })}
            />
            <strong className="p-ml-2">{vencedor.licitante.nome}</strong>
          </div>
        </div>
        <div className="flex-right p-mr-2">
          <div className="feedback">
            {className?.includes('panel-check') && (
              <span className={`circle check`}>
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-warning') && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-error') && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    ),
    [toggleLote, collapsed]
  );

  const _renderDataTable = useCallback(
    (itens) => (
      <DataTable value={itens} rowHover emptyMessage="Nenhum item disponível" className="p-datatable-sm">
        {columns.map((c, idx) => (
          <Column key={`col-${idx}`} {...c} />
        ))}
      </DataTable>
    ),
    [columns]
  );

  if (readOnly) {
    return (
      <div className="flex flex-column">
        {store.itensGroupByDetentor?.map((v) => (
          <Resumo
            key={v.licitante.id}
            vencedor={v}
            decimalPlaces={decimalPlaces}
            showDesconto={showDesconto}
            labelLicitante="Fornecedor/Prestador"
            lotes={store.lotes}
          />
        ))}
        <DialogDetalhes visible={visible} setVisible={setVisible} item={selectedItem} />
      </div>
    );
  }

  return (
    <div>
      <div className="p-fluid p-formgrid p-grid">
        <div className="p-field p-col-3">
          <strong className="p-text-capitalize">Fornecedor/Prestador</strong>
          <Dropdown
            value={store.vencedor || store.vencedoresGrouped?.[0]}
            onChange={handleVencedorChange}
            optionLabel="licitante.nome"
            options={store.vencedoresGrouped}
            placeholder="Selecione um Licitante"
          />
        </div>
        <div className="p-field p-col-3" style={{ paddingRight: '30px' }}>
          <strong className="p-text-capitalize">Lote</strong>
          <Dropdown
            id="dropdown-lote"
            value={store.lote}
            onChange={handleLoteChange}
            optionLabel="nome"
            options={optionsLotesByVencedor}
            placeholder="Selecione um lote"
            showClear
          />
        </div>
      </div>

      <VirtualizedPickList
        source={sourceOptions}
        target={targetOptions}
        onChange={handlePickListChange}
        itemTemplate={itemTemplate}
        dataKey="id"
      />
      <div className="p-mt-2">
        {store.itensGroupByDetentor?.map((venc) => {
          const isAllFilled = venc.itens.every((item) => item.preenchido);
          const className = isAllFilled ? 'panel-check' : 'panel-warning';
          return (
            <Panel
              key={venc.licitante.id}
              className={className}
              header={_renderHeader(venc, className)}
              content={_renderDataTable(venc.itens)}
              collapsed={collapsed[venc.licitante?.id]}
            />
          );
        })}
      </div>
      <DialogDetalhes visible={visible} setVisible={setVisible} item={selectedItem} />
    </div>
  );
});

MemoizedItemTemplate.propTypes = {
  store: PropTypes.object.isRequired,
  item: PropTypes.object.isRequired,
  vencedor: PropTypes.object,
};

export default ItensCarona;
