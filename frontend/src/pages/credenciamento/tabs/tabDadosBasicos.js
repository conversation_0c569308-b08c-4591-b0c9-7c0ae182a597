import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import FormField from 'fc/components/FormField';
import { SelectButton } from 'primereact/selectbutton';
import moment from 'moment';
import { Divider } from 'primereact/divider';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TermoSelectDetails from '~/pages/licitacao/tabs/termoDetails';
import FcButton from 'fc/components/FcButton';
import { getValue, getValueByKey, getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { Dropdown } from 'primereact/dropdown';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import SelectDialog from 'fc/components/SelectDialog';
import ComissaoIndexStore from '~/stores/comissao/indexStore';
import FcDropdown from 'fc/components/FcDropdown';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import ComissaoFormPage from '~/pages/licitacao/formComissao';
import AsyncMultiselect from 'fc/components/AsyncMultiselect';
import { Fieldset } from 'primereact/fieldset';
import TermoIndexStore from '~/stores/licitacao/termoIndex';
import { ConfirmDialog } from 'primereact/confirmdialog';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FcCalendar from 'fc/components/FcCalendar';
import { Dialog } from 'primereact/dialog';
import ObraMapContainer from '~/pages/licitacao/tabs/ObraMapContainer';
import { InputTextarea } from 'primereact/inputtextarea';
import Obra from '~/domains/Obra';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { isValueValid } from 'fc/utils/utils';
import { RadioButton } from 'primereact/radiobutton';

@observer
class TabDadosBasicos extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;

    this.state = {
      idRemove: null,
      currentLesgilacao: undefined,
      dialogVisibility: false,
      visibleDialogObras: false,
    };
    this._onCreateStateObra = this._onCreateStateObra.bind(this);
    this._onCreate = this._onCreate.bind(this);
    this._onDelete = this._onDelete.bind(this);
    this.onClick = this.onClick.bind(this);
    this.onHide = this.onHide.bind(this);
  }

  componentDidMount() {
    this.store.initializeTabDadosBasicos();
    if (isValueValid(this.store.idCredenciamento)) {
      this.store.getCredenciados();
    }
    (this.props.action === 'edit' || this.store.enableReqMod) && this.store.loadTipos();
    this.store.getPessoasResponsaveis(() => this.forceUpdate());
  }

  getDateAttributeValue(value) {
    return value ? moment(value).toDate() : value;
  }

  _onCreateStateObra(event) {
    const layerType = event?.layerType;
    const coordinates = [];
    this.store?.updateStateObraAttribute('tipoCamadaObraDTO', layerType);
    if (['polyline', 'polygon'].includes(layerType)) {
      const latLngs = 'polyline' === layerType ? event.layer.getLatLngs() : event.layer.getLatLngs()[0];
      latLngs.forEach((element) => {
        const coor = [];
        coor.push(element.lat);
        coor.push(element.lng);
        coordinates.push(coor);
      });
      this.store?.updateStateObraAttribute('coordenadasObraDTO', latLngs);
    } else {
      coordinates.push(event.layer.getLatLng().lat);
      coordinates.push(event.layer.getLatLng().lng);
      this.store?.updateStateObraAttribute('coordenadasObraDTO', [event.layer.getLatLng()]);
    }
    this.store?.updateStateObraAttribute('coordenadas', coordinates.join(','));
  }

  _onCreate() {
    const { coordenadas, coordenadasObraDTO, tipoCamadaObraDTO } = this.store.stateObra;
    if (coordenadas) {
      this.store.updateObraDTOAtt('tipoCamada', tipoCamadaObraDTO);
      this.store.updateObraDTOAtt('coordenadas', coordenadasObraDTO);
      this.store.updateAttribute('coordenadas', coordenadas);
    }
  }

  _onDelete() {
    this.store.updateAttribute('coordenadas', undefined);
  }

  onClick(name, position) {
    let state = { [`${name}`]: true };

    if (position) {
      state = { ...state, position };
    }

    this.setState(state);
  }

  onHide(name) {
    this.setState({ [`${name}`]: false });
  }

  _toggleDialogObras() {
    this.store.resetStateObra();
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '0px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  renderFooterObras() {
    return (
      <div>
        <FcButton
          label="Confirmar"
          icon="pi pi-check"
          onClick={() => {
            this._onCreate();
            this._toggleDialogObras();
          }}
          className="p-button-text"
        />
      </div>
    );
  }

  _renderDialogObras() {
    return (
      <Dialog
        header="Cadastrar Localização de Obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={this.renderFooterObras()}
        onHide={() => this._toggleDialogObras()}
      >
        <ObraMapContainer
          hasEdificacao={!!this.store.object?.obra?.edificacao?.localizacao}
          tipoSelecao={this.store.object.tipoSelecao}
          previousSelectedObra={this.store.object?.obra?.edificacao?.localizacao}
          selectedObra={this.store.obraObject}
          onCreated={this._onCreateStateObra}
          onDeleted={this._onDelete}
        />
      </Dialog>
    );
  }

  _naturezaHasObra(selectedNaturezas) {
    return (
      selectedNaturezas && selectedNaturezas.some((natureza) => ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].includes(natureza))
    );
  }

  validateField(field) {
    return this.store.rules[field] && this.store.rules[field].error && this.state.submitted
      ? { className: 'p-invalid p-error' }
      : {};
  }

  checkPresencaCredenciados() {
    return this.store.credenciados.length === 0;
  }

  _renderDialog() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibility}
        message="Ao trocar de legislação, os arquivos selecionados serão removidos do sistema. Deseja continuar e aplicar a mudança na legislação, removendo os arquivos selecionados?"
        header="Atualização da Legislação"
        onHide={() => {
          this._toggleDialogVisibility();
        }}
        accept={() => {
          this.store.setArquivos([]);
          this.store.fileStore.removeAllFiles();
          this.store.updateAttribute('lei', this.state.currentLesgilacao);
          this.store.updateAttribute('termoReferencia', null);
          this.store.updateAttribute('legislacaoOutros', '');
          this.store.loadTipos();
          this.setState({ currentLesgilacao: undefined });
        }}
      />
    );
  }

  render() {
    const { submitted } = this.props;
    const { getRule } = this.store;

    const columnsTermo = [
      {
        field: 'identificadorProcesso',
        header: 'Identificador',
        body: ({ identificadorProcesso, lotes }) => {
          return lotes?.length ? (
            <div>{getValue(identificadorProcesso)}</div>
          ) : (
            <div className="actions p-d-flex align-items-center">
              <FcButton
                icon="pi pi-exclamation-triangle"
                tooltip="Termo não permite associação, pois não possui itens/lotes cadastrados."
                className="p-button-sm p-button-danger p-button-text"
              />
              {getValue(identificadorProcesso)}
            </div>
          );
        },
        sortable: true,
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
    ];

    const columnsCommissao = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'tipo',
        header: 'Tipo',
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTipoComissao()),
      },
      {
        field: 'tipoConjunto',
        header: 'Conjunto',
        sortable: true,
        body: ({ tipoConjunto }) => getValueByKey(tipoConjunto, DadosEstaticosService.getTipoConjuntoComissao()),
      },
      {
        field: 'dataVigenciaInicial',
        header: 'Início da Vigência',
        sortable: true,
        body: ({ dataVigenciaInicial }) => getValueDate(dataVigenciaInicial, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'dataVigenciaFinal',
        header: 'Fim da Vigência',
        sortable: true,
        body: ({ dataVigenciaFinal }) => getValueDate(dataVigenciaFinal, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
    ];

    const comissaoForm = (props) => {
      return <ComissaoFormPage action="new" closeMethod={props.closeMethod} />;
    };

    return (
      <>
        {this.store.enableReqMod && (
          <Fieldset legend="AVISO">
            <h6 style={{ color: '#dd0303' }}>
              A EDIÇÃO DESTE CREDENCIAMENTO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
            </h6>
          </Fieldset>
        )}
        <div className="p-col-12">
          {this._renderDivider('')}
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={
                  this.store.object?.lei === 'OUTRA'
                    ? {
                        sm: 12,
                        md: 5,
                        lg: 5,
                        xl: 5,
                      }
                    : {
                        sm: 12,
                        md: 6,
                        lg: 6,
                        xl: 6,
                      }
                }
                attribute="lei"
                label="O Credenciamento será regida por qual legislação?"
                rule={getRule('lei')}
                submitted={submitted}
              >
                <SelectButton
                  optionLabel="text"
                  optionValue="value"
                  value={this.store.object.lei}
                  options={DadosEstaticosService.getTipoLicitacaoSomenteLei14133()}
                  onChange={(e) => {
                    if (e.value !== null) {
                      if (this.store.arquivos?.length) {
                        this.setState({ currentLesgilacao: e.value });
                        this._toggleDialogVisibility();
                      } else {
                        this.store.updateAttribute('lei', e.value);
                        this.store.updateAttribute('termoReferencia', null);
                        this.store.updateAttribute('legislacaoOutros', '');
                        this.forceUpdate();
                      }
                    }
                  }}
                />
              </FormField>
              {this.state.dialogVisibility && this._renderDialog()}
              {this.store.object?.lei === 'OUTRA' && (
                <FormField
                  rule={getRule('legislacaoOutros')}
                  columns={{
                    sm: 12,
                    md: 3,
                    lg: 3,
                    xl: 3,
                  }}
                  attribute="legislacaoOutros"
                  label="Outra Lei"
                  infoTooltip="Informe sob qual lei o processo está sendo criado"
                  submitted={submitted}
                >
                  <InputText
                    value={this.store.object.legislacaoOutros}
                    placeholder="Informe a legislação"
                    rows={4}
                    onChange={(e) => this.store.updateAttribute('legislacaoOutros', e)}
                  />
                </FormField>
              )}
              {!this.store.object.processoMigrado && (
                <FormField
                  columns={
                    this.store.object?.lei === 'OUTRA'
                      ? {
                          sm: 12,
                          md: 4,
                          lg: 4,
                          xl: 4,
                        }
                      : {
                          sm: 12,
                          md: 6,
                          lg: 6,
                          xl: 6,
                        }
                  }
                  attribute="participacaoExclusiva"
                  label="Destinado exclusivamente à participação de microempresas e empresas de pequeno porte?"
                  rule={getRule('participacaoExclusiva')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row">
                    <div className="p-field-radiobutton p-col mb-0">
                      <RadioButton
                        inputId="participacaoExclusiva"
                        name="participacaoExclusiva"
                        value={true}
                        onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                        checked={this.store.object.participacaoExclusiva}
                      />
                      <label htmlFor="participacaoExclusiva">Sim</label>
                    </div>
                    <div className="p-field-radiobutton p-col mb-0">
                      <RadioButton
                        inputId="semparticipacaoExclusiva"
                        name="senParticipacaoExclusiva"
                        value={false}
                        onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                        checked={this.store.object.participacaoExclusiva === false}
                      />
                      <label htmlFor="senParticipacaoExclusiva">Não</label>
                    </div>
                  </div>
                </FormField>
              )}
            </div>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                rule={getRule('numeroProcesso')}
                columns={{
                  sm: 12,
                  md: 5,
                  lg: 5,
                  xl: 5,
                }}
                attribute="numeroProcesso"
                label="Número do Processo Administrativo"
                infoTooltip="Deve ser informado um número de processo, que pode ser o número do processo do SEI (caso tenha), ou o número do Processo Administrativo que gerou o credenciamento"
                submitted={submitted}
              >
                <InputText
                  maxLength={30}
                  onInput={(e) => {
                    const value = e.target.value.replace(/[^0-9./-]/g, '');
                    this.store.updateAttribute('numeroProcesso', value);
                  }}
                  value={this.store.object.numeroProcesso}
                  placeholder="Informe o  número do processo"
                  rows={4}
                />
              </FormField>
              <FormField
                rule={getRule('numero')}
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="numero"
                label="Número do Credenciamento"
                submitted={submitted}
              >
                <InputText
                  maxLength={4}
                  onInput={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    this.store.updateAttribute('numero', value);
                  }}
                  value={this.store.object.numero}
                  placeholder="Informe o número do credenciamento"
                  rows={4}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 3,
                  lg: 3,
                  xl: 3,
                }}
                attribute="ano"
                label="Ano"
                rule={getRule('ano')}
                submitted={submitted}
              >
                <Dropdown
                  onChange={(e) => this.store.updateAttribute('ano', e)}
                  placeholder="Informe o ano"
                  value={this.store.object?.ano}
                  id="ano"
                  optionLabel="text"
                  optionValue="value"
                  options={this.store.anos}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="inicioVigencia"
                label="Início da Vigência"
                rule={getRule('inicioVigencia')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.inicioVigencia)}
                  onChange={(e) => this.store.updateAttributeDate('inicioVigencia', e)}
                  showIcon
                  mask="99/99/9999"
                  maxDate={this.getDateAttributeValue(this.store.object.fimVigencia)}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="fimVigencia"
                label="Fim da Vigência"
                rule={getRule('fimVigencia')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.fimVigencia)}
                  onChange={(e) => this.store.updateAttributeDate('fimVigencia', e)}
                  showIcon
                  mask="99/99/9999"
                  minDate={this.getDateAttributeValue(this.store.object.inicioVigencia)}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="tipoContratacao"
                label="Tipo de Contratação"
                rule={getRule('tipoContratacao')}
                submitted={submitted}
              >
                <FcDropdown
                  inOrder
                  id="tipoContratacao"
                  options={DadosEstaticosService.getTipoContratacao()}
                  optionLabel="text"
                  optionValue="value"
                  value={this.store.object.tipoContratacao}
                  onChange={(e) => this.store.updateAttribute('tipoContratacao', e)}
                  placeholder="Selecione o tipo de contratação"
                  showClear
                  showFilterClear
                  filter
                  emptyMessage="Nenhum Registro Encontrado"
                  emptyFilterMessage="Nenhum Registro Encontrado"
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="termoReferencia"
                label="Termo de Referência"
                rule={getRule('termoReferencia')}
                submitted={submitted}
              >
                <TermoSelectDetails
                  value={this.store.object.termoReferencia}
                  label="identificadorProcesso"
                  indexStore={new TermoIndexStore()}
                  onChange={(e) => this.store.updateAttribute('termoReferencia', e)}
                  emptyMessage="Selecione o termo"
                  dialogColumns={columnsTermo}
                  searchFields={['identificadorProcesso']}
                  filterSuggest={this.store.getFilterSuggestTermo()}
                  disabledComponent={!this.checkPresencaCredenciados()}
                  radioMode
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 5,
                  lg: 5,
                  xl: 5,
                }}
                attribute="responsavelCredenciamento"
                label="Responsável pelo Credenciamento"
                rule={getRule('responsavelCredenciamento')}
                submitted={submitted}
              >
                <Dropdown
                  id="responsavelCredenciamento"
                  value={this.store.object.idResponsavelCredenciamento}
                  optionLabel="nome"
                  optionValue="id"
                  options={this.store.responsaveisCredenciamento}
                  onChange={(e) => {
                    this.store.updateResponsavelCredenciamentoAttribute('idResponsavelCredenciamento', e.value);
                  }}
                  placeholder="Selecione o(a) responsável"
                  emptyMessage="Não há responsáveis disponíveis"
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 7,
                  lg: 7,
                  xl: 7,
                }}
                attribute="sitioDivulgacao"
                label="Sítio de Divulgação do Edital de Chamamento Público"
                rule={getRule('sitioDivulgacao')}
                submitted={submitted}
              >
                <InputText
                  onChange={(e) => this.store.updateAttribute('sitioDivulgacao', e)}
                  placeholder="Informe o sítio de divulgação do edital de chamamento público"
                  value={this.store.object.sitioDivulgacao}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="comissaoContratacao"
                label="Comissao de Contratação"
                rule={getRule('comissaoContratacao')}
                submitted={submitted}
              >
                <SelectDialog
                  value={this.store.object.comissaoContratacao}
                  label={null}
                  indexStore={new ComissaoIndexStore()}
                  onChange={(e) => {
                    this.store.updateAttribute('comissaoContratacao', e);
                    this.forceUpdate();
                  }}
                  onClear={() => this.store.updateAttribute('comissaoContratacao', undefined)}
                  headerDialog="Comissão de Contratação"
                  emptyMessage="Selecione a comissão de contratação"
                  nullMessage={
                    this.store.object.comissaoContratacao &&
                    this.store.object.comissaoContratacao.numero +
                      ' - ' +
                      getValueByKey(
                        this.store.object.comissaoContratacao.tipo,
                        DadosEstaticosService.getTipoComissao()
                      ) +
                      ' - ' +
                      getValueByKey(
                        this.store.object.comissaoContratacao.tipoConjunto,
                        DadosEstaticosService.getTipoConjuntoComissao()
                      )
                  }
                  dialogColumns={columnsCommissao}
                  filterSuggest={this.store.comissaoFilterSuggest()}
                  searchFields={['numero']}
                  canCreate
                  formPage={comissaoForm}
                />
              </FormField>
              <FormField
                rule={getRule('presidenteComissao')}
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="presidenteComissao"
                label={'Presidente da Comissão'}
                submitted={submitted}
              >
                <Dropdown
                  onChange={(e) =>
                    this.store.updateAttributePresidenteComissao(
                      'presidenteComissao',
                      e,
                      this.store?.object?.comissaoContratacao?.membros
                    )
                  }
                  placeholder={'Selecione o presidente da comissão'}
                  value={this.store.object?.presidenteComissao?.id ?? null}
                  id="presidenteComissao"
                  optionLabel="nome"
                  optionValue="id"
                  emptyMessage="Nenhuma comissão de contratação foi selecionada"
                  options={this.store?.object?.comissaoContratacao?.membros ?? null}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="naturezasDoObjeto"
                label="Naturezas do Objeto"
                rule={getRule('naturezasDoObjeto')}
                submitted={submitted}
              >
                <FcMultiSelect
                  onChange={(e) => {
                    const naturezasSelecionadas = e?.target?.value;

                    this.store.updateAttribute('naturezasDoObjeto', e);
                    this.store.loadTipos();
                    if (this._naturezaHasObra(naturezasSelecionadas) && !this.store?.object?.obra) {
                      this.store.updateAttribute('tipoSelecao', 'PONTO');
                      this.store.updateAttribute('obra', new Obra());
                      this.store.updateObraAttribute('finalizada', false);
                    } else if (!this._naturezaHasObra(naturezasSelecionadas) && this.store?.object?.obra) {
                      this.store.updateAttribute('obra', undefined);
                      this.store.updateAttribute('tipoObra', undefined);
                      this.store.updateAttribute('categoria', undefined);
                      this.store.updateAttribute('coordenadas', '');
                      this.store.initializeObraDTO();
                    }
                  }}
                  placeholder="Selecione as naturezas do objeto"
                  value={this.store.object.naturezasDoObjeto}
                  options={DadosEstaticosService.getNaturezaObjetoLicitacao()}
                  optionValue="value"
                  optionLabel="text"
                  filterBy="text"
                  filter
                  selectedItemsLabel="{} itens selecionados"
                  showClear
                  showOverlay
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="fontesDeRecurso"
                label="Fontes de Recurso"
                rule={getRule('fontesDeRecurso')}
                submitted={submitted}
              >
                <FcMultiSelect
                  placeholder="Selecione as fontes de recurso"
                  value={this.store.object.fontesDeRecurso}
                  onChange={(e) => this.store.updateAttribute('fontesDeRecurso', e)}
                  options={this.store.fontesRecursos}
                  showOverlay
                  optionLabel="nome"
                  filterBy="nome"
                  filter
                  selectedItemsLabel="{} itens selecionados"
                  showClear
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="orgaosParticipantes"
                label="Órgãos Participantes"
                rule={getRule('orgaosParticipantes')}
                submitted={submitted}
              >
                <AsyncMultiselect
                  placeholder="Selecione os órgãos participantes"
                  value={this.store.object?.orgaosParticipantes}
                  onChange={(e) => this.store.updateAttribute('orgaosParticipantes', e)}
                  store={this.store.orgaosParticipantesStore}
                  label="nome"
                  showOverlay
                />
              </FormField>
              {this._naturezaHasObra(this.store?.object?.naturezasDoObjeto) && (
                <>
                  <FormField
                    columns={{
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }}
                    attribute="tipoObra"
                    label="Tipo da Obra"
                    rule={getRule('tipoObra')}
                    submitted={submitted}
                  >
                    <AsyncDropdown
                      onChange={(_, v) => {
                        this.store.updateObraAttribute('tipo', v);
                        this.store.updateAttribute('tipoObra', v);
                        this.forceUpdate();
                      }}
                      value={this.store.object.obra?.tipo?.id}
                      placeholder="Selecione o tipo da obra"
                      store={this.store.obraTipoStore}
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                  <FormField
                    columns={{
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }}
                    attribute="categoria"
                    label="Categoria da Obra"
                    rule={getRule('categoria')}
                    submitted={submitted}
                  >
                    <AsyncDropdown
                      onChange={(_, v) => {
                        this.store.updateObraAttribute('categoria', v);
                        this.store.updateAttribute('categoria', v);
                        this.forceUpdate();
                      }}
                      value={this.store.object.obra?.categoria?.id}
                      placeholder="Selecione a categoria da obra"
                      store={this.store.obraCategoriaStore}
                      disabled={this.props.readOnly}
                    />
                  </FormField>

                  <FormField
                    columns={{
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }}
                    attribute="tipoSelecao"
                    label="Tipo Seleção"
                    rule={getRule('tipoSelecao')}
                    submitted={submitted}
                  >
                    <div className="flex gap-2">
                      <FcDropdown
                        inOrder
                        {...this.validateField('tipoSelecao')}
                        onChange={(e) => this.store.updateAttribute('tipoSelecao', e)}
                        placeholder="Selecione o tipo de seleção"
                        value={this.store.object?.tipoSelecao}
                        id="tipoSelecao"
                        optionLabel="text"
                        optionValue="value"
                        disabled={this.props.readOnly}
                        options={DadosEstaticosService.getTipoSelecaoMapa()}
                        className="w-6"
                      />
                      <FcButton
                        label="Selecionar no Mapa"
                        type="button"
                        className="p-button-secondary w-6"
                        onClick={() => this._toggleDialogObras()}
                        loading={this.store.loading}
                        disabled={!this.store.object.tipoSelecao || this.props.readOnly}
                      />
                    </div>
                  </FormField>

                  <FormField
                    columns={{
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }}
                    attribute="coordenadas"
                    label="Coordenadas Geográficas"
                    rule={getRule('coordenadas')}
                    submitted={submitted}
                  >
                    <InputTextarea
                      onChange={(e) => updateAttribute('coordenadas', e)}
                      value={this.store.object.coordenadas}
                      disabled
                      rows={4}
                    />
                  </FormField>
                </>
              )}
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="objeto"
                label="Objeto"
                rows={5}
                rule={getRule('objeto')}
                submitted={submitted}
              >
                <FcInputTextarea
                  rows={5}
                  cols={30}
                  autoResize
                  value={this.store.object.objeto}
                  onChange={(e) => this.store.updateAttribute('objeto', e)}
                  placeholder="Informe o objeto do processo"
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="observacoes"
                label="Observações"
                rows={5}
                rule={getRule('observacoes')}
                submitted={submitted}
              >
                <FcInputTextarea
                  rows={5}
                  cols={30}
                  autoResize
                  value={this.store.object.observacoes}
                  onChange={(e) => this.store.updateAttribute('observacoes', e)}
                  placeholder="Informe as observações do processo"
                />
              </FormField>
              <FormField
                columns={12}
                attribute="arquivos"
                label="Arquivos"
                submitted={submitted}
                rule={getRule('arquivos')}
              >
                <MultipleFileUploader
                  store={this.store.fileStore}
                  onChangeFiles={(files) => this.store.setArquivos(files)}
                  fileTypes={DadosEstaticosService.getTipoArquivoCredenciamento()}
                />
              </FormField>
            </div>
          </form>
          {this._renderDialogObras()}
        </div>
      </>
    );
  }
}

TabDadosBasicos.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
  readOnly: PropTypes.bool,
  action: PropTypes.string,
};

export default TabDadosBasicos;
