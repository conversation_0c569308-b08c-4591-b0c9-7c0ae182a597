.panel-lote {
  display: flex;
  width: 100%;
  border: 1px solid;
  border-color: rgb(192, 192, 192);
  padding-top: 10px;
  margin-bottom: 10px;
  flex-direction: column;
}

.panel-not-collapsed {
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
}

.panel-collapsed {
  border-radius: 5px;
}

.panel-check {
  background-color: #caf1d8;
}

.panel-warning {
  background-color: #faedc4;
}

.panel-error {
  background-color: #ffd0ce;
}

.header {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
}

.flex-left {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: center;
  margin-right: auto;

  strong {
    align-self: center;
  }
}

.flex-right {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  align-self: center;
  margin-left: auto;

  div,
  strong {
    align-self: center;
  }
}

.info-lote {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.feedback {
  margin-left: 10px;

  .circle {
    display: flex;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    justify-content: center;
    margin-right: 10px;

    i,
    span {
      align-self: center;
      color: white;
    }

    span {
      font-size: 20px;
    }
  }

  .check {
    background-color: #22c55e;
  }

  .warning {
    background-color: #eab308;
  }

  .error {
    background-color: #ff3d32;
  }
}

.pointer {
  cursor: pointer;
}

.default {
  cursor: default;
}

.text-disabled {
  color: #9e9e9e;
}

.bg-white {
  background-color: #ffffff;
}

.item-container-c {
  display: flex;
  flex-direction: row;
}

.others-c {
  margin-bottom: 17px;
}

.qtd-c {
  min-width: 120px;
}

.unit-c {
  min-width: 120px;
}

.actions-container {
  display: inline-flex;
  flex-direction: row; 
  width: auto !important;
}

@media (max-width: 960px) {
  .item-container-c {
    margin-bottom: 0;
    margin-top: 0;
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .p-datatable .p-datatable-thead > tr > th,
  .p-datatable .p-datatable-tbody > tr > td {
    display: block; 
    width: 100%;
    text-align: center; 
  }

  .p-datatable .p-datatable-thead > tr > th {
    padding: 8px 5px;
  }

  .p-datatable .p-datatable-tbody > tr > td {
    padding: 8px 5px; 
    display: flex;
    flex-direction: column;
    text-align: center; 
  }

  .p-datatable .p-datatable-tbody .action-buttons {
    display: flex;
    justify-content: center; 
    gap: 8px; 
  }

  .p-datatable .p-datatable-tbody .action-buttons button {
    flex: 1;
    max-width: 100px; 
  }

  .p-datatable .p-datatable-tbody .input-below .p-inputtext,
  .p-datatable .p-datatable-tbody .input-below .p-inputnumber {
    width: 100%;
  }
}




