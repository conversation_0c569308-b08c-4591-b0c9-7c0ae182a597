import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AditivoContratoFormStore from '~/stores/contrato/aditivo/formStore';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import FormField from 'fc/components/FormField';
import { Fieldset } from 'primereact/fieldset';
import { InputTextarea } from 'primereact/inputtextarea';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { isValueValid, showNotification } from 'fc/utils/utils';
import FcButton from 'fc/components/FcButton';
import { Dialog } from 'primereact/dialog';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import RequisicaoModificacaoContratoFormStore from '~/stores/contrato/requisicaoModificacao/formStore';
import AppStore from 'fc/stores/AppStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { ProgressSpinner } from 'primereact/progressspinner';
import FcCalendar from 'fc/components/FcCalendar';
import FcMultiSelect from 'fc/components/FcMultiSelect';

@observer
class AditivoContratoFormPage extends GenericFormPage {
  reqModificacaoStore;
  constructor(props) {
    super(
      props,
      UrlRouter.cadastrosConsulta.contrato.aditivo.index.replace(':idContrato', props.idContrato),
      AccessPermission.aditivoContrato
    );

    this.store = new AditivoContratoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoContratoFormStore();

    this.state = {
      showDialogReqMod: false,
      errorDialogValue: false,
    };

    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
  }

  componentDidMount() {
    const { id, idContrato, action } = this.props;
    this.store.initialize(id, { idContrato: idContrato, status: 'PUBLICADA' }, () => {
      action === 'edit' && this.store.checkDataCadastro();
      action === 'edit' && this.store.carregaDadosComplementaresAditivo(id);
      this.store.carregaContrato(idContrato, () => {
        if (action !== 'edit') {
          this.store.preencheResponsaveis();
        }
      });
    });
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.reqModificacaoStore.loading}
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa: ">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            {hasWritePermission && !this.store.enableReqMod && (
              <FcButton label="Salvar" type="button" loading={this.store.loading} onClick={this.submitFormData} />
            )}
            {hasWritePermission && this.store.enableReqMod && (
              <FcButton
                label="Enviar Requisição"
                type="button"
                onClick={() => this._toggleDialogReqMod()}
                loading={this.store.loading}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  submitFormData() {
    const execution = () => {
      if (!this.store.rules.hasError) {
        if (this.store.validateSubmittedFiles(this.store.arquivoAditivoContratoList)) {
          const aditivoDTO = {
            aditivoContrato: this.store.object,
            arquivosAditivo: this.store.arquivoAditivoContratoList,
          };
          !this.store.enableReqMod && this.store.save(this._goBack, this.props.action);
          this.store.enableReqMod &&
            this.reqModificacaoStore.justificativaJurisdicionado &&
            this.reqModificacaoStore.enviarRequisicaoAditivoContrato(aditivoDTO, this._goBack);
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  isOptionDisabled(option, selectedValues) {
    if (!selectedValues || selectedValues.length === 0) return false;

    const valoresSelecionados = selectedValues.map((v) => (typeof v === 'string' ? v : v.value));

    if (valoresSelecionados.includes('ALTERACAO_PRAZO') && option === 'REDUCAO_VIGENCIA') {
      return true;
    }
    if (valoresSelecionados.includes('REDUCAO_VIGENCIA') && option === 'ALTERACAO_PRAZO') {
      return true;
    }
    if (valoresSelecionados.includes('ALTERACAO_VALOR') && option === 'SUPRESSAO_VALOR') {
      return true;
    }
    if (valoresSelecionados.includes('SUPRESSAO_VALOR') && option === 'ALTERACAO_VALOR') {
      return true;
    }

    return false;
  }

  render() {
    const { getRule } = this.store;
    const { props, submitFormData } = this;
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Contrato', url: UrlRouter.cadastrosConsulta.contrato.index },
      {
        label: 'Alteração Contratual',
        url: UrlRouter.cadastrosConsulta.contrato.aditivo.index.replace(':idContrato', props.idContrato),
      },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object && this.store.contrato && !this.store.loadingDadosComplementares) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              {this.store.enableReqMod && (
                <Fieldset legend="AVISO">
                  <h6 style={{ color: '#dd0303' }}>
                    A EDIÇÃO DESTE CONTRATO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                  </h6>
                </Fieldset>
              )}
              <Fieldset legend="Dados da Alteração Contratual">
                <div className="p-fluid p-formgrid p-grid">
                  <FormField
                    columns={{
                      sm: 12,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    }}
                    attribute="numero"
                    label="Número"
                    rule={getRule('numero')}
                  >
                    <InputText
                      id="numero"
                      value={this.store.object.numero}
                      onChange={(e) => this.store.updateAttribute('numero', e.target.value)}
                      maxLength={20}
                      placeholder="Número da alteração contratual"
                    />
                  </FormField>
                  <FormField
                    columns={{
                      sm: 12,
                      md: 8,
                      lg: 8,
                      xl: 8,
                    }}
                    attribute="tiposAlteracao"
                    label="Tipos de alteração contratual"
                    rule={getRule('tiposAlteracao')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      onChange={(e) => this.store.settiposAlteracao(e)}
                      placeholder="Selecione os tipos da alteração contratual"
                      value={this.store.object.tiposAlteracao}
                      options={DadosEstaticosService.getTiposAlteracaoContratual().map((option) => ({
                        ...option,
                        disabled: this.isOptionDisabled(option.value, this.store.object.tiposAlteracao),
                      }))}
                      optionValue="value"
                      optionLabel="text"
                      filterBy="text"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                      sortable={false}
                      showOverlay
                    />
                  </FormField>

                  {this.store.isAdiamentoVigencia() && (
                    <>
                      <FormField
                        columns={{
                          sm: 12,
                          md: 6,
                          lg: 6,
                          xl: 6,
                        }}
                        attribute="dataVigenciaInicial"
                        label="Início da Vigência"
                        rule={getRule('dataVigenciaInicial')}
                        submitted={submitted}
                      >
                        <FcCalendar
                          value={this.getDateAttributeValue(this.store.object.dataVigenciaInicial)}
                          showIcon
                          disabled
                        />
                      </FormField>

                      <FormField
                        columns={{
                          sm: 12,
                          md: 6,
                          lg: 6,
                          xl: 6,
                        }}
                        attribute="dataVigenciaFinal"
                        label="Fim da Vigência"
                        rule={getRule('dataVigenciaFinal')}
                        submitted={submitted}
                      >
                        <FcCalendar
                          value={this.getDateAttributeValue(this.store.object.dataVigenciaFinal)}
                          onChange={(e) => this.store.updateAttributeDate('dataVigenciaFinal', e)}
                          showIcon
                        />
                      </FormField>
                    </>
                  )}

                  {this.store.isAdiamentoValor() && (
                    <FormField
                      columns={4}
                      attribute="valor"
                      label={
                        this.store.object.tiposAlteracao.includes('SUPRESSAO_VALOR') ? 'Valor a deduzir' : 'Novo Valor'
                      }
                      rule={getRule('valor')}
                      submitted={submitted}
                      infoTooltip="Deverá ser preenchido com o novo valor do contrato, independente do valor específico do aditivo/supressão."
                    >
                      <InputMonetary
                        onChange={(e) => this.store.updateAttribute('valor', e)}
                        placeholder="R$"
                        mode="currency"
                        value={this.store.object.valor}
                        disabled={!this.store.isAdiamentoValor()}
                      />
                    </FormField>
                  )}

                  <FormField
                    columns={12}
                    attribute="justificativa"
                    label="Justificativa"
                    rule={getRule('justificativa')}
                    submitted={submitted}
                  >
                    <InputTextarea
                      onChange={(e) => this.store.updateAttribute('justificativa', e)}
                      placeholder="Digite a justificativa"
                      rows={4}
                      value={this.store.object.justificativa}
                    />
                  </FormField>

                  <FormField
                    columns={{
                      sm: 12,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    }}
                    attribute="dataPublicacao"
                    label="Data da Publicação"
                    rule={getRule('dataPublicacao')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataPublicacao)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('dataPublicacao', e)}
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                </div>
              </Fieldset>
              <div className="p-col-12">
                <Fieldset legend="Arquivos">
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    onChangeFiles={(files) => this.store.setArquivoAditivoContratoList(files)}
                    fileTypes={DadosEstaticosService.getTipoArquivoAditivo()}
                  />
                </Fieldset>
              </div>
              {this.renderActionButtons()}
              {this.renderDialogRequisicaoModificacao()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

AditivoContratoFormPage.propTypes = {
  id: PropTypes.any,
  idContrato: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default AditivoContratoFormPage;
