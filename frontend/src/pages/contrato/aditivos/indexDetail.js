import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { Divider } from 'primereact/divider';
import { getMultipleValuesByKey, getValueDate, getValueMoney } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AditivoContratoFormStore from '~/stores/contrato/aditivo/formStore';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import classNames from 'classnames';
import AppStore from 'fc/stores/AppStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';

@observer
class AditivoDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.aditivoStore = new AditivoContratoFormStore();
  }

  componentDidMount() {
    const { aditivo } = this.props;
    this.aditivoStore.initialize(aditivo.id, {}, () => {
      this.aditivoStore.carregaDadosComplementaresAditivo(aditivo.id);
      this.aditivoStore.carregaUltimaAlteracao(aditivo?.id);
    });
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return {
      'py-0': tipoLayout === 'compacto',
      'py-2': tipoLayout === 'default' || tipoLayout === 'extenso',
    };
  }

  _renderValue(label, value, col = 12) {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>
        <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
          {value ?? '-'}
        </div>
      </div>
    );
  }

  _renderTabs() {
    const tabs = [
      {
        id: 0,
        header: 'Arquivos',
        content: (
          <MultipleFileUploader
            fileTypes={DadosEstaticosService.getTipoArquivoAditivo()}
            downloadOnly
            store={this.aditivoStore.fileStore}
          />
        ),
      },
    ];

    return <FcCloseableTabView tabs={tabs} />;
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  render() {
    if (this.props.aditivo) {
      const { aditivo } = this.props;
      const { contrato } = aditivo;
      const tiposAlteracao = getMultipleValuesByKey(
        aditivo.tiposAlteracao,
        DadosEstaticosService.getTiposAlteracaoContratual()
      );

      return (
        <div className="p-fluid p-form">
          <div>{this._renderDivider('Detalhes')}</div>
          {this._renderValue('Contrato', `${contrato?.numeroContrato}`, 12)}
          {this._renderValue('Tipo de Alteração Contratual', tiposAlteracao, 12)}
          {this.aditivoStore.isAdiamentoVigencia() && (
            <>
              {this._renderValue('Início da Vigência', getValueDate(aditivo.dataVigenciaInicial), 12)}
              {this._renderValue('Final da Vigência', getValueDate(aditivo.dataVigenciaFinal), 12)}
            </>
          )}
          {this._renderValue('Data da Publicação', getValueDate(aditivo.dataPublicacao), 12)}
          {this.aditivoStore.isAdiamentoValor() && this._renderValue('Valor', getValueMoney(aditivo.valor), 12)}
          {this._renderValue('Justificativa', aditivo.justificativa, 12)}
          <div>{this._renderDivider('Gerenciamento')}</div>
          {aditivo.id && (
            <div>
              {this._renderValue(
                'Cadastrado por',
                aditivo.usuario
                  ? `${aditivo.usuario.nome} em ${getValueDate(
                      aditivo.dataCadastro,
                      DATE_FORMAT_WITH_HOURS,
                      DATE_PARSE_FORMAT_WITH_HOURS
                    )}`
                  : '-',
                12
              )}
              {this._renderValue(
                'Alterado por',
                this.aditivoStore.ultimaAlteracao
                  ? `${this.aditivoStore.ultimaAlteracao?.nome} em ${getValueDate(
                      this.aditivoStore.ultimaAlteracao?.data,
                      DATE_FORMAT_WITH_HOURS,
                      DATE_PARSE_FORMAT_WITH_HOURS
                    )}`
                  : '-',
                12
              )}
            </div>
          )}
          <Divider />
          {this._renderTabs()}
        </div>
      );
    } else {
      return <div>Erro ao exibir detalhes do Aditivo.</div>;
    }
  }
}

AditivoDetailPage.propTypes = {
  aditivo: PropTypes.any,
};

export default AditivoDetailPage;
