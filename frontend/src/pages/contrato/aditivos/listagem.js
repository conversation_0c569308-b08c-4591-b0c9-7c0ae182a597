import React from 'react';
import { observer } from 'mobx-react';
import AditivoContratoIndexStore from '~/stores/contrato/aditivo/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import { getValueDate, getValueMoney, getValue, checkUserGroup, getMultipleValuesByKey } from 'fc/utils/utils';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class AditivoContratoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.aditivoContrato);
    this.store = new AditivoContratoIndexStore();
    this.store.setIdContrato(props.match.params.idContrato);
    this.state = {
      selectedRow: null,
      showRequisicaoRemocao: false,
    };
    this._renderDialogRequisicaoRemocao = this._renderDialogRequisicaoRemocao.bind(this);
    this._handleRemocaoRequisicaoModal = this._handleRemocaoRequisicaoModal.bind(this);
    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  componentDidMount() {
    this.store.carregaContrato(() => this.forceUpdate());
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.rowSelected}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="aditivo"
      />
    );
  }

  _handleRemocaoRequisicaoModal(aditivoContrato) {
    this.setState({ showRequisicaoRemocao: true, rowSelected: aditivoContrato });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, rowSelected: undefined });
  }

  render() {
    const isAuditor = checkUserGroup('Auditor');

    const columns = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'tiposAlteracao',
        header: 'Tipo de Alteração Contratual',
        body: ({ tiposAlteracao }) =>
          getMultipleValuesByKey(tiposAlteracao, DadosEstaticosService.getTiposAlteracaoContratual()),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor',
        body: ({ valor }) => getValueMoney(valor),
        sortable: true,
      },
      {
        field: 'dataVigenciaFinal',
        header: 'Fim da Vigência',
        body: (rowData) => this.store.getVigenciaFinal(rowData),
        sortable: true,
      },
      {
        field: 'justificativa',
        header: 'Justificativa',
        body: ({ justificativa }) => getValue(justificativa),
        sortable: true,
      },
      {
        field: 'dataPublicacao',
        header: 'Data da Publicação',
        body: ({ dataPublicacao }) => getValueDate(dataPublicacao),
        sortable: true,
      },
      {
        style: { width: '180px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              {rowData.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Requisição de modificação pendente"
                    className="p-button-sm p-button-info p-mr-1"
                    onClick={() =>
                      this.pushUrlToHistory(
                        isAuditor
                          ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(
                              ':id',
                              rowData.idRequisicaoModificacao
                            )
                          : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                              ':id',
                              rowData.idRequisicaoModificacao
                            )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <div>
                  <PermissionProxy resourcePermissions={this.getReadPermission()}>
                    <FcButton
                      icon="pi pi-eye"
                      className="p-button-sm p-mr-2"
                      tooltip="Detalhes"
                      onClick={() => this.props.onDetail(rowData)}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-pencil"
                      tooltip={
                        this.store.isRequisicaoModificacao(rowData.dataCadastro)
                          ? 'Criar Requisição de Modificação'
                          : 'Editar'
                      }
                      className="p-button-sm p-button-success p-mr-2"
                      onClick={() =>
                        this.pushUrlToHistory(
                          this.store.isRequisicaoModificacao(rowData.dataCadastro)
                            ? UrlRouter.administracao.requisicaoModificacao.aditivo.requisitar
                                .replace(':idContrato', this.store.idContrato)
                                .replace(':id', rowData.id)
                            : UrlRouter.cadastrosConsulta.contrato.aditivo.editar
                                .replace(':idContrato', this.store.idContrato)
                                .replace(':id', rowData.id)
                        )
                      }
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-trash"
                      tooltip="Remover"
                      className="p-button-sm p-button-danger"
                      onClick={() => this._handleRemocaoRequisicaoModal(rowData)}
                    />
                  </PermissionProxy>
                </div>
              )}
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() =>
              this.pushUrlToHistory(
                UrlRouter.cadastrosConsulta.contrato.aditivo.novo.replace(':idContrato', this.store.idContrato)
              )
            }
            disabled={!this.store.contrato?.permiteAditivo}
            tooltip={this.store.contrato?.motivoRescisao ? 'Contrato Rescindido não permite Aditivo' : ''}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const filterSuggest = [
      {
        id: '',
        field: 'contrato',
        operator: 'EQUAL_TO',
        value: this.store.idContrato,
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'contrato',
          label: 'Contrato',
          type: SearchTypes.TEXT,
        },
      },
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numero']}
          filterSuggest={filterSuggest}
        />
        <IndexDataTable
          columns={columns}
          value={listKey}
          header={header}
          loading={loading}
          {...getDefaultTableProps()}
        />
        {this._renderDialogRequisicaoRemocao()}
      </>
    );
  }
}

AditivoContratoListagemPage.displayName = 'AditivoContratoListagemPage';

export default AditivoContratoListagemPage;
