import React from 'react';
import './style.scss';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { Divider } from 'primereact/divider';
import { getValueMoney, getValue<PERSON>y<PERSON>ey, getValue, getValueDate, checkUserGroup } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DataTable } from 'primereact/datatable';
import moment from 'moment';
import { Column } from 'react-virtualized';
import FcButton from 'fc/components/FcButton';
import { FeatureGroup, MapContainer, Marker, Polygon, Polyline, TileLayer } from 'react-leaflet';
import { Dialog } from 'primereact/dialog';
import OcorrenciaLicitacaoDetailPage from '~/pages/ocorrenciaLicitacao/ocorrenciaLicitacaoDetailPage';
import BoardLicitacoesIndexStore from '~/stores/boardLicitacoes/indexStore';
import { Link } from 'react-router-dom';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import ApresentacaoForm from '~/stores/licitacao/tabs/apresentacaoForm';
import AppStore from 'fc/stores/AppStore';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ContratoIndexStore from '~/stores/contrato/indexStore';
import ListagemContratoIndex from './listagemContratos';
import MapeamentoAtosLicitacoesFormStore from '~/stores/mapeamentoAtosLicitacoes/formStore';
import classNames from 'classnames';
import Vencedores from '~/pages/vencedores';
import { getNumberUnitThousands, generateFullURL } from 'fc/utils/utils';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import LicitacaoFormStore from '~/stores/licitacao/formStore';
import GerenciamentoTermoFormStore from '~/stores/gerenciamentoTermo/formStore';

@observer
class LicitacaoDetailPage extends React.Component {
  _isMounted = false;
  fileStore;
  licitacaoStore;
  termoReferenciaStore;
  mapeamentoStore = new MapeamentoAtosLicitacoesFormStore();
  constructor(props) {
    super(props);
    this.boardsStore = new BoardLicitacoesIndexStore();
    this.fileStore = this.boardsStore.fileStore;
    this.licitacaoStore = props.store ?? new LicitacaoFormStore();
    this.contratoStore = new ContratoIndexStore();
    this.storeApresentacao = new ApresentacaoForm();
    this.termoReferenciaStore = new GerenciamentoTermoFormStore();
    this.state = {
      visibleDialogObras: false,
      showDetalhesDialog: false,
      isModalVisible: false,
      selectedRow: null,
      numeroDownloads: 0,
      activeTabIndex: 0,
      statusProcessamento: null,
      atos: [],
      termoReferenciaExists: false,
    };
    this._toggleDOEContentDialog = this._toggleDOEContentDialog.bind(this);
  }

  componentDidMount() {
    this.licitacaoStore.initialize(this.props.id, {}, (idTermo) => {
      this.licitacaoStore.hasAlert(this.props.id, () => this.forceUpdate());
      this.licitacaoStore.carregaUltimaAlteracao(this.props.id);
      this.licitacaoStore.initializeTdaLicitacao(this.props.id);
      this.mapeamentoStore.getAtoByLicitacaoDoeId(this.props.id, (atos) => {
        this.setState({ atos });
      });
      this.boardsStore.initialize(this.props.id);
      this.storeApresentacao.initialize(this.props.id);
      this.setState({ numeroDownloads: this.licitacaoStore.object.numeroDownloads ?? 0 });
      idTermo &&
        this.termoReferenciaStore.initialize(idTermo, {}, () => {
          this.termoReferenciaStore.recuperarArquivos(idTermo);
          this.setState({ termoReferenciaExists: true });
          this.setState({ activeTabIndex: 0 });
        });
      const editalExists = !!this.licitacaoStore.edital;
      this.props.onEditalExists?.(editalExists);
      this.licitacaoStore?.edital && this.licitacaoStore.getStatusEditalProcessamento(() => this.forceUpdate());
    });
  }

  componentWillUnmount() {
    this.licitacaoStore.updatingStatusEditalProcessamento = false;
    if (this.licitacaoStore.intervalId) {
      clearInterval(this.licitacaoStore.intervalId);
    }
  }

  hasAtos() {
    return this.state.atos.length > 0;
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column className="p-p-3" key={`col-${col.field}`} {...col} />);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return {
      'py-0': tipoLayout === 'compacto',
      'py-2': tipoLayout === 'default' || tipoLayout === 'extenso',
    };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.length ? value.map((value) => <div className={`details-value p-text-justify`}>{value}</div>) : '-'}
          </div>
        )}

        {type == 'link' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}

        {type == 'button' && <div className="details-value p-text-justify p-0">{value ?? '-'}</div>}
      </div>
    );
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _toggleDialogObras() {
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  _toggleDOEContentDialog() {
    this.setState((prevState) => ({
      isModalVisible: !prevState.isModalVisible,
    }));
  }

  renderEdificacao(licitacao) {
    const obra = licitacao?.obra?.edificacao?.localizacao;
    const polyline = [];
    const polygon = [];
    let marker = [];
    if (obra) {
      if (obra.type === 'Polygon') {
        polygon.push(obra.coordinates[0]);
      } else if (obra.type === 'LineString') {
        polyline.push(obra.coordinates);
      } else {
        marker = obra.coordinates;
      }
    }
    return (
      <>
        {marker && marker.length > 0 && <Marker position={marker} radius={20} />}
        {polyline && polyline.length > 0 && <Polyline positions={polyline} />}
        {polygon && polygon.length > 0 && <Polygon positions={polygon} />}
      </>
    );
  }

  _renderDialogObras(licitacao) {
    let defaultCoordinates = [-8.921198844909668, -70.98129272460939];

    const obra = licitacao?.obra?.edificacao?.localizacao;

    if (obra) {
      if (obra.type === 'Polygon') {
        defaultCoordinates = obra.coordinates[0][0];
      } else if (obra.type === 'LineString') {
        defaultCoordinates = obra.coordinates[0];
      } else {
        defaultCoordinates = obra.coordinates;
      }
    }

    return (
      <Dialog
        header="Localização de obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={[]}
        onHide={() => this._toggleDialogObras()}
      >
        <div>
          <MapContainer center={defaultCoordinates} zoom={16} style={{ height: '70vh', width: '100wh' }}>
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <FeatureGroup />

            {obra && this.renderEdificacao(licitacao)}
          </MapContainer>
        </div>
      </Dialog>
    );
  }

  _renderButton() {
    return (
      <FcButton
        label="Visualizar no mapa"
        type="button"
        className="p-button-secondary"
        icon="pi pi-map"
        onClick={() => this._toggleDialogObras()}
      />
    );
  }

  verificaFormaPublicacao(tpFormaPublicacao, descricao, pagina) {
    let descricaoCampo = descricao ?? '';
    const descPrefix = descricao && /^\d*\.?\d+$/.test(descricao) ? 'Nº ' : '';
    const pagPrefix = pagina && /^\d*\.?\d+$/.test(pagina) ? 'pág. ' : '';

    if (
      DadosEstaticosService.getFormaPublicacaoPaginada().some((e) => e.value === tpFormaPublicacao) &&
      (pagina || descricao)
    ) {
      descricaoCampo =
        pagina && descricao
          ? `${descPrefix} ${descricao}, ${pagPrefix} ${pagina}`
          : pagina
          ? `${pagPrefix} ${pagina}`
          : `${descPrefix} ${descricao}`;
    }
    return descricaoCampo;
  }

  _renderTabPublicacoes(licitacao) {
    const columns = [
      {
        field: 'tpFormaPublicacao',
        header: 'Nome',
        body: ({ tpFormaPublicacao }) => tpFormaPublicacao.nome,
      },
      {
        header: 'Descrição',
        body: (rowData) =>
          this.verificaFormaPublicacao(rowData.tpFormaPublicacao.nome, rowData.descricao, rowData.pagina),
      },
      {
        field: 'dataPublicacao',
        header: 'Data de Publicação',
        body: ({ dataPublicacao }) => moment(dataPublicacao).format(DATE_FORMAT),
      },
    ];

    return (
      <DataTable
        rowHover
        value={licitacao.publicacoes}
        paginator
        rows={5}
        className="p-datatable-sm"
        emptyMessage="Nenhuma publicação adicionada."
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  renderDescricaoMaterial({ descricao, material }) {
    return `${material ? material.descricao + ' ' : ''}${descricao}`;
  }

  getValue(oldValue, newValue, valueFunc, inline = false) {
    if (valueFunc) {
      oldValue = valueFunc(oldValue);
      newValue = newValue && valueFunc(newValue);
    }
    return newValue && newValue !== oldValue ? (
      <div className={inline && 'p-d-flex p-ml-1'}>
        <div className={inline && 'p-mr-1'} style={{ textDecoration: 'line-through' }}>
          {oldValue}
        </div>
        {newValue}
      </div>
    ) : (
      <>{getValue(oldValue)}</>
    );
  }

  _renderDetalhesModificacaoDialog() {
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Detalhes da Modificação</h4>
      </div>
    );

    return (
      <Dialog
        header={header}
        visible={this.state.showDetalhesDialog}
        style={{ width: '30vw' }}
        onHide={() => this.setState({ showDetalhesDialog: false })}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <div>
          <Divider />
          <div className="p-grid">
            {this._renderValue(
              'Motivo',
              getValueByKey(this.state.selectedRow?.motivoAlteracao, DadosEstaticosService.getMotivoAlteracao()),
              12
            )}
            {this._renderValue('Descrição', this.state.selectedRow?.obsMotivoAlteracao, 12)}
          </div>
        </div>
      </Dialog>
    );
  }

  _renderTabContratos(idLicitacao) {
    return (
      <ListagemContratoIndex
        idLicitacao={idLicitacao}
        onDetailContratoAssociado={this.props.onDetailContratoAssociado}
      />
    );
  }

  _renderTabParticipantes() {
    const columnsParticipantes = [
      {
        style: { width: '30%' },
        field: 'nome',
        header: 'Nome',
      },
      {
        style: { width: '30%' },
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
      },
      {
        field: 'pessoaFisica',
        header: 'Tipo de Pessoa',
        body: ({ pessoaFisica }) => getValueByKey(pessoaFisica, DadosEstaticosService.getTipoPessoa()),
        style: { width: '20%' },
      },
      {
        field: 'internacional',
        header: 'Pessoa/Empresa Internacional',
        body: ({ internacional }) => getValueByKey(internacional, DadosEstaticosService.getSimNao()),
        style: { width: '20%' },
      },
    ];
    return (
      <DataTable rowHover value={this.licitacaoStore.object.licitantes} paginator rows={5} className="p-datatable-sm">
        {this._renderColumns(columnsParticipantes)}
      </DataTable>
    );
  }

  _renderTabs(licitacao) {
    const tabs = [];
    tabs.push({
      id: 0,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          countDownloadRequest={this.props.countDownloadRequest}
          fileTypesToCount={['EDITAL_PROJETO_BASICO']}
          downloadOnly
          store={this.fileStore}
          fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
          accept=".pdf, .xls, .xlsx"
          onDownload={(tipo) => {
            if (tipo === 'EDITAL_PROJETO_BASICO') {
              this.props.countDownloadRequest && this.setState({ numeroDownloads: this.state.numeroDownloads + 1 });
              licitacao.numeroDownloads = this.state.numeroDownloads;
            }
          }}
        />
      ),
    });
    if (this.state.termoReferenciaExists && !this.props.isTermoDetail) {
      tabs.push({
        id: 1,
        header: 'Arquivos do Termo de Referência',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={this.termoReferenciaStore?.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoTermoReferencia()}
            accept=".pdf, .xls, .xlsx"
          />
        ),
      });
    }
    tabs.push({ id: 2, header: 'Publicações', content: this._renderTabPublicacoes(licitacao) });
    tabs.push({
      id: 3,
      header: 'Histórico de Atualizações',
      content: (
        <OcorrenciaLicitacaoDetailPage
          ocorrenciaList={licitacao.ocorrencias}
          legislacao={licitacao?.lei}
          arquivos={this.fileStore.uploadedFiles}
        />
      ),
    });
    ['APRESENTACAO_PROPOSTAS_LANCES', 'FINALIZACAO'].includes(licitacao.fase) &&
      tabs.push({
        id: 4,
        header: 'Licitantes',
        content: this._renderTabParticipantes(),
      });
    licitacao.fase === 'FINALIZACAO' &&
      ['APRESENTACAO_PROPOSTAS_LANCES', 'FINALIZACAO'].includes(licitacao.fase) &&
      tabs.push({ id: 6, header: 'Vencedores', content: this._renderTabItens(licitacao) }) &&
      tabs.push({
        id: 5,
        header: 'Contratos Associados',
        content: this._renderTabContratos(licitacao.id),
      });

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderTabItens() {
    return (
      <>
        {this.storeApresentacao && (
          <Vencedores
            readOnly
            store={this.storeApresentacao.vencedoresStore}
            labelLicitante="vencedor"
            entidadeAntiga={
              this.storeApresentacao.isLegislacaoAntiga ||
              this.storeApresentacao.licitacao?.modalidadeLicitacaoNova?.nome === 'Leilão'
            }
            showDesconto={this.storeApresentacao.licitacao?.tiposLicitacao
              ?.map((t) => t.nome?.toUpperCase())
              ?.includes('MAIOR DESCONTO')}
            valueFuncReqModificacao={this.getValue}
          />
        )}
      </>
    );
  }

  _renderTitle(licitacao) {
    const title = `${
      licitacao.modalidade
        ? getValueByKey(licitacao.modalidade, DadosEstaticosService.getModalidadeLicitacao())
        : getValue(licitacao.modalidadeLicitacaoNova?.nome)
    } - ${getValue(licitacao.numero)}/${licitacao.ano}`;
    return licitacao.entidade ? title + ` - ${licitacao.entidade.nome}` : title;
  }

  _renderTitleModal(value, color = '#9E9E9E') {
    return <span style={{ color }}>{value}</span>;
  }

  _getTaggedValuesFromText(text) {
    return <span dangerouslySetInnerHTML={{ __html: text }} />;
  }

  _renderValueModal(
    label,
    value,
    col = 12,
    iconTitle = undefined,
    colorIconTitle = undefined,
    backgroundColorIconTitle = undefined,
    iconValue = undefined,
    colorIconValue = undefined
  ) {
    return (
      <div className={`p-col-${col}`}>
        <div className="p-col-12 p-d-flex p-ai-baseline gap-2" style={{ fontWeight: 'bold' }}>
          {label}
          {iconTitle && this._renderIcon(iconTitle, colorIconTitle, backgroundColorIconTitle)}
        </div>
        <div className="p-col-12 p-d-flex p-ai-baseline gap-2">
          {iconValue && this._renderIcon(iconValue, colorIconValue)}
          {value}
        </div>
      </div>
    );
  }

  _renderIcon(icon, color, backgroundColor) {
    return (
      <i
        className={icon}
        style={{
          color,
          backgroundColor,
          borderRadius: '50%',
          padding: '2px',
        }}
      />
    );
  }

  render() {
    const licitacao = this.licitacaoStore.object;
    const tentativasMatch = licitacao?.tentativasMatch ?? 0;
    const { edital } = this.licitacaoStore;
    const { numeroDownloads } = this.state;
    const { countDownloadRequest } = this.props;
    const { idLicitacao } = this.licitacaoStore;
    const isAuditorOrAdmin = checkUserGroup(['Auditor', 'Administrador']);
    const srp =
      licitacao?.lei === 'LEI_N_8666' || licitacao?.modalidadeLicitacaoNova?.nome === 'Leilão'
        ? licitacao?.srp
        : licitacao?.termoReferencia?.srp;
    if (licitacao) {
      return (
        <div className="detail-padding">
          <div className="p-fluid p-form">
            <div id={`detalhes-${idLicitacao}`}>{this._renderDivider('Detalhes')}</div>
            {this._renderValue('Licitação', this._renderTitle(licitacao), 12)}
            {this._renderValue(
              'Valor Total Estimado',
              getValueMoney(licitacao.valorEstimado, licitacao?.termoReferencia?.tresCasasDecimais ? 3 : 2)
            )}
            {['APRESENTACAO_PROPOSTAS_LANCES', 'FINALIZACAO'].includes(licitacao.fase) &&
              licitacao.valorAdjudicado > 0 &&
              this._renderValue('Valor Total Adjudicado', getValueMoney(licitacao?.valorAdjudicado))}
            {this._renderValue('Objeto', licitacao.objeto)}
            {this._renderValue(
              'Data de Abertura',
              getValueDate(licitacao.dataAbertura, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
            )}
            {licitacao.dataTransferencia &&
              this._renderValue(
                'Data da Transferência',
                getValueDate(licitacao.dataTransferencia, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
              )}
            <>
              {isAuditorOrAdmin && (
                <>
                  <div id={`robosAnaliseAutomatica-${idLicitacao}`}>
                    {this._renderDivider('Robôs de Análise Automática')}
                  </div>
                  {this._renderValue(
                    'Análise do Edital',
                    edital ? (
                      this.licitacaoStore.statusEditalProcessamento === 'EM_EXTRACAO' ||
                      this.licitacaoStore.statusEditalProcessamento === 'EM_CLASSIFICACAO' ||
                      !this.licitacaoStore.statusEditalProcessamento ? (
                        'Em processamento'
                      ) : (
                        <span
                          style={{ cursor: 'pointer', color: 'blue', textDecoration: 'underline' }}
                          onClick={() => this.props.onDetailEdital(this.licitacaoStore.edital)}
                        >
                          Concluído
                        </span>
                      )
                    ) : (
                      'Não processado'
                    )
                  )}
                  {this._renderValue(
                    'Leitura do DOE',
                    this.hasAtos() ? (
                      <span
                        onClick={this._toggleDOEContentDialog}
                        style={{ cursor: 'pointer', color: 'blue', textDecoration: 'underline' }}
                      >
                        Publicação encontrada
                      </span>
                    ) : tentativasMatch >= 3 ? (
                      'Publicação não encontrada'
                    ) : (
                      'Aguardando Associação com Ato do DOE'
                    )
                  )}
                  <Dialog
                    visible={this.state.isModalVisible}
                    onHide={this._toggleDOEContentDialog}
                    className="custom-dialog"
                  >
                    <Divider align="left">
                      <b style={{ fontSize: '16px' }}>{`Ato de Licitação no DOE-AC - ${this.state.atos[0]?.id}`}</b>
                    </Divider>
                    <div className="p-grid p-col-12">
                      {this._renderValueModal(
                        this._renderTitleModal('Data da Publicação'),
                        getValueDate(this.state.atos[0]?.diarioOficial?.dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
                        3,
                        '',
                        '',
                        '',
                        'pi pi-calendar',
                        '#EAB308'
                      )}
                      {this._renderValueModal(
                        this._renderTitleModal('Órgão/Entidade'),
                        getValue(this.state.atos[0]?.ente),
                        3,
                        '',
                        '',
                        '',
                        'pi pi-user',
                        '#14B8A6'
                      )}
                      {this._renderValueModal(
                        this._renderTitleModal('Identificação'),
                        getValue(this.state.atos[0]?.identificacao),
                        3,
                        '',
                        '',
                        '',
                        'pi pi-tag',
                        '#4CD07D'
                      )}
                    </div>
                    <div className="p-grid p-col-12 mt-2">
                      {this._renderValueModal(
                        this._renderTitleModal('Objeto'),
                        getValue(this.state.atos[0]?.objeto),
                        12,
                        '',
                        '',
                        '',
                        'pi pi-file',
                        '#326FD1'
                      )}
                    </div>
                    <div className="p-grid p-col-12 p-mt-2 justify-content-end text-justify">
                      {this._renderValueModal(
                        this._renderTitleModal('Texto'),
                        this.hasAtos()
                          ? this._getTaggedValuesFromText(
                              this.mapeamentoStore._getTaggedValuesFromText(this.state.atos[0])
                            )
                          : '',
                        12
                      )}
                    </div>
                  </Dialog>
                </>
              )}
            </>
            <div id={`informacoesLegais-${idLicitacao}`}>{this._renderDivider('Informações Legais')}</div>
            {licitacao.termoReferencia && !this.props.isTermoDetail && (
              <>
                {AppStore.hasPermission(AccessPermission.gerenciamentoTermo.readPermission) ||
                AppStore.hasPermission(AccessPermission.gerenciamentoTermo.writePermission)
                  ? this._renderValue(
                      'Termo de Referência',
                      licitacao.termoReferencia.identificadorProcesso,
                      12,
                      'link',
                      UrlRouter.termoReferencia.gerenciamentoTermos.detalhe.replace(
                        ':idTermo',
                        licitacao.termoReferencia.id
                      )
                    )
                  : this._renderValue('Termo de Referência', licitacao.termoReferencia.identificadorProcesso, 12)}
              </>
            )}
            {this._renderValue(
              'Sistema de Registro de Preços (SRP)',
              getValueByKey(srp, DadosEstaticosService.getSimNao()),
              12
            )}
            {this._renderValue(
              'Destinado exclusivamente à participação de microempresas e empresas de pequeno porte',
              getValueByKey(licitacao.participacaoExclusiva, DadosEstaticosService.getSimNao())
            )}
            {licitacao.lei === 'LEI_N_14133'
              ? this._renderValue(
                  'Critérios de Julgamento',
                  licitacao.tiposLicitacao?.map((tipo) => tipo?.nome),
                  12,
                  'list'
                )
              : this._renderValue('Critérios de Julgamento', licitacao.tipo?.nome)}
            {licitacao.processoMigrado
              ? this._renderValue('Tipo do Critério', licitacao.forma.nome, 12)
              : this._renderValue(
                  'Tipo do Critério',
                  getValueByKey(licitacao.tipoAdjudicacao, DadosEstaticosService.getTipoAdjudicacaoLicitacao()),
                  12
                )}
            {this._renderValue(
              'Naturezas do Objeto',
              licitacao?.naturezasDoObjeto?.map((n) =>
                getValueByKey(n, DadosEstaticosService.getNaturezaObjetoLicitacao())
              ),
              12,
              'list'
            )}
            {this._renderValue('Número do Processo Administrativo', licitacao.numeroProcessoAdm)}
            {this._renderValue(
              'Fontes de Recurso',
              licitacao?.fontesDeRecurso.map((f) => f.nome),
              12,
              'list'
            )}
            {this._renderValue(
              'Órgãos Participantes',
              licitacao?.orgaosParticipantes.map((op) => op.nome),
              12,
              'list'
            )}
            {this._renderValue(
              'Comissão',
              licitacao.comissao ? `${licitacao.comissao.numero} / ${licitacao.comissao.tipo}` : '-'
            )}
            {this._renderValue('Pregoeiro', getValue(licitacao?.pregoeiro?.nome))}
            {this._renderValue('Regência Legal', licitacao.regenciaLegal)}
            {countDownloadRequest && this._renderValue('Número de Downloads', getNumberUnitThousands(numeroDownloads))}
            <div id={`gerenciamento-${idLicitacao}`}>{this._renderDivider('Gerenciamento')}</div>
            {licitacao.id && (
              <>
                {this._renderValue(
                  'Cadastrado por',
                  licitacao.usuario
                    ? `${licitacao.usuario.nome} em ${getValueDate(
                        licitacao.dataCadastroPreparatoria,
                        DATE_FORMAT_WITH_HOURS,
                        DATE_PARSE_FORMAT_WITH_HOURS
                      )}`
                    : '-'
                )}
                {licitacao.dataCadastro &&
                  licitacao.userCreatedPublicacao &&
                  this._renderValue(
                    'Divulgação e Publicação por',
                    `${licitacao.userCreatedPublicacao.nome} em ${getValueDate(
                      licitacao.dataCadastro,
                      DATE_FORMAT_WITH_HOURS,
                      DATE_PARSE_FORMAT_WITH_HOURS
                    )}`
                  )}
                {this._renderValue(
                  'Alterado por',
                  licitacao.userUpdated && licitacao.updatedAt
                    ? `${licitacao.userUpdated.nome} em ${getValueDate(
                        licitacao.updatedAt,
                        DATE_FORMAT_WITH_HOURS,
                        DATE_PARSE_FORMAT_WITH_HOURS
                      )}`
                    : '-'
                )}
              </>
            )}
            {licitacao.obra && (
              <>
                {this._renderDivider('Obra')}
                {this._renderValue('Tipo da Obra', licitacao.obra?.tipo?.nome)}
                {this._renderValue('Categoria da Obra', licitacao.obra?.categoria?.nome)}
                {this._renderValue('Obra', licitacao.obra?.edificacao ? this._renderButton(3) : '-', 12, 'button')}
              </>
            )}
          </div>
          <Divider style={{ marginBottom: `1px` }} />
          {this.licitacaoStore.hasPermissionAlerta() && this.licitacaoStore.idAlerta && (
            <FcButton
              icon="pi pi-bell"
              label="Histórico de Alertas"
              className="p-button-danger my-3"
              onClick={() => {
                window.open(
                  generateFullURL(UrlRouter.alerta.editar.replace(':id', this.licitacaoStore.idAlerta)),
                  '_blank'
                );
              }}
            />
          )}
          {this._renderTabs(licitacao)}
          {this._renderDialogObras(licitacao)}
          {this._renderDetalhesModificacaoDialog()}
        </div>
      );
    } else {
      return (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        />
      );
    }
  }
}

LicitacaoDetailPage.defaultProps = {
  countDownloadRequest: false,
  isTermoDetail: false,
};

LicitacaoDetailPage.propTypes = {
  id: PropTypes.number,
  fileStore: PropTypes.any,
  countDownloadRequest: PropTypes.bool,
  onDetailContratoAssociado: PropTypes.func,
  onDetailEdital: PropTypes.func,
  onEditalExists: PropTypes.func,
  store: PropTypes.any,
  isTermoDetail: PropTypes.bool,
};

export default LicitacaoDetailPage;
