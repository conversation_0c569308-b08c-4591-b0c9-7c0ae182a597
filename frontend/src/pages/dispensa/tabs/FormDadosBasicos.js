import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import Form<PERSON>ield from 'fc/components/FormField';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import moment from 'moment';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { getValueDate, getValue } from 'fc/utils/utils';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import InputMonetary from 'fc/components/InputMonetary';
import { Fieldset } from 'primereact/fieldset';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import PermissionProxy from 'fc/components/PermissionProxy';
import { AutoComplete } from 'primereact/autocomplete';
import { Divider } from 'primereact/divider';
import TermoSelectDetails from '~/pages/licitacao/tabs/termoDetails';
import FcButton from 'fc/components/FcButton';
import { SelectButton } from 'primereact/selectbutton';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dropdown } from 'primereact/dropdown';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import FcCalendar from 'fc/components/FcCalendar';
import AsyncMultiselect from 'fc/components/AsyncMultiselect';
import FcDropdown from 'fc/components/FcDropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import Obra from '~/domains/Obra';
import { Dialog } from 'primereact/dialog';
import ObraMapContainer from 'pages/licitacao/tabs/ObraMapContainer';
import { RadioButton } from 'primereact/radiobutton';

@observer
class FormDadosBasicos extends React.Component {
  constructor(props) {
    super(props);
    this.store = this.props.store;

    this.state = {
      ...this.state,
      dialogVisibility: false,
      currentLesgilacao: undefined,
      visibleDialogObras: false,
    };

    this._renderDialogObras = this._renderDialogObras.bind(this);
    this._toggleDialogObras = this._toggleDialogObras.bind(this);
    this._onCreateStateObra = this._onCreateStateObra.bind(this);
    this._onCreate = this._onCreate.bind(this);
    this._onDelete = this._onDelete.bind(this);
    this.onClick = this.onClick.bind(this);
    this.onHide = this.onHide.bind(this);
  }

  componentDidMount() {
    this.store.initializeTabDadosBasicos(() => this.forceUpdate());
    this.store.getPessoasResponsaveis(() => this.forceUpdate());
    (this.props.action === 'edit' || this.store.enableReqMod) && this.store.loadTipos();
  }

  _onCreateStateObra(event) {
    const layerType = event?.layerType;
    const coordinates = [];
    this.store?.updateStateObraAttribute('tipoCamadaObraDTO', layerType);
    if (['polyline', 'polygon'].includes(layerType)) {
      const latLngs = 'polyline' === layerType ? event.layer.getLatLngs() : event.layer.getLatLngs()[0];
      latLngs.forEach((element) => {
        const coor = [];
        coor.push(element.lat);
        coor.push(element.lng);
        coordinates.push(coor);
      });
      this.store?.updateStateObraAttribute('coordenadasObraDTO', latLngs);
    } else {
      coordinates.push(event.layer.getLatLng().lat);
      coordinates.push(event.layer.getLatLng().lng);
      this.store?.updateStateObraAttribute('coordenadasObraDTO', [event.layer.getLatLng()]);
    }
    this.store?.updateStateObraAttribute('coordenadas', coordinates.join(','));
  }

  _onCreate() {
    const { coordenadas, coordenadasObraDTO, tipoCamadaObraDTO } = this.store.stateObra;
    if (coordenadas) {
      this.store.updateObraDTOAtt('tipoCamada', tipoCamadaObraDTO);
      this.store.updateObraDTOAtt('coordenadas', coordenadasObraDTO);
      this.store.updateAttribute('coordenadas', coordenadas);
    }
  }

  _onDelete() {
    this.store.updateAttribute('coordenadas', undefined);
  }

  onClick(name, position) {
    let state = { [`${name}`]: true };

    if (position) {
      state = { ...state, position };
    }

    this.setState(state);
  }

  onHide(name) {
    this.setState({ [`${name}`]: false });
  }

  _toggleDialogObras() {
    this.store.resetStateObra();
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  renderFooterObras() {
    return (
      <div>
        <FcButton
          label="Confirmar"
          icon="pi pi-check"
          onClick={() => {
            this._onCreate();
            this._toggleDialogObras();
          }}
          className="p-button-text"
        />
      </div>
    );
  }

  _renderDialogObras() {
    return (
      <Dialog
        header="Cadastrar Localização de Obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={this.renderFooterObras()}
        onHide={() => this._toggleDialogObras()}
      >
        <ObraMapContainer
          hasEdificacao={!!this.store.object?.obra?.edificacao?.localizacao}
          tipoSelecao={this.store.object?.tipoSelecao}
          previousSelectedObra={this.store.object?.obra?.edificacao?.localizacao}
          selectedObra={this.store.obraObject}
          onCreated={this._onCreateStateObra}
          onDeleted={this._onDelete}
        />
      </Dialog>
    );
  }

  _renderDialog() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibility}
        message="Ao trocar de legislação, os arquivos selecionados serão removidos do sistema. Deseja continuar e aplicar a mudança na legislação, removendo os arquivos selecionados?"
        header="Atualização da Legislação"
        onHide={() => {
          this._toggleDialogVisibility();
        }}
        accept={() => {
          this.store.setArquivoDispensaList([]);
          this.store.fileStore.removeAllFiles();
          this.store.updateAttribute('lei', this.state.currentLesgilacao);
          this.store.updateAttribute('termoReferencia', null);
          this.store.updateAttribute('legislacaoOutros', '');
          this.setState({ currentLesgilacao: undefined });
          this.store.loadTipos(this.store.object.lei);
          this.forceUpdate();
        }}
      />
    );
  }

  validateField(field) {
    return this.store.rules[field] && this.store.rules[field].error && this.state.submitted
      ? { className: 'p-invalid p-error' }
      : {};
  }

  _naturezaHasObra(selectedNaturezas) {
    return (
      selectedNaturezas && selectedNaturezas.some((natureza) => ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].includes(natureza))
    );
  }

  _toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  renderNumeroProcessoSei() {
    const { submitted } = this.props;
    const { updateAttribute, getRule } = this.store;
    let attribute = 'numeroProcessoSEI';
    if (this.store.isProcessoMigrado()) {
      attribute = 'numeroProcesso';
    }
    return (
      <FormField
        columns={{
          sm: 12,
          md: 6,
          lg: 6,
          xl: 6,
        }}
        attribute={attribute}
        label="Número do Processo Administrativo"
        infoTooltip="Deve ser informado um número de processo, que pode ser o número do processo do SEI (caso tenha), ou o número do Processo Administrativo que gerou a dispensa"
        rule={getRule(attribute)}
        submitted={submitted}
      >
        <InputText
          maxLength={30}
          onInput={(e) => {
            const value = e.target.value.replace(/[^0-9./-]/g, '');
            updateAttribute(attribute, value);
          }}
          placeholder="Informe o número do processo"
          value={this.store.object[attribute]}
        />
      </FormField>
    );
  }

  render() {
    const { submitted } = this.props;
    const { updateAttribute, getRule } = this.store;

    const columnsTermo = [
      {
        field: 'identificadorProcesso',
        header: 'Identificador',
        body: ({ identificadorProcesso, lotes }) => {
          return lotes?.length ? (
            <div>{getValue(identificadorProcesso)}</div>
          ) : (
            <div className="actions p-d-flex align-items-center">
              <FcButton
                icon="pi pi-exclamation-triangle"
                tooltip="Termo não permite associação, pois não possui itens/lotes cadastrados."
                className="p-button-sm p-button-danger p-button-text"
              />
              {getValue(identificadorProcesso)}
            </div>
          );
        },
        sortable: true,
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
    ];

    let content;

    if (this.store.object && !this.store.loadingTabDadosBasicos) {
      const isMigrado = this.store.object?.processoMigrado;
      content = (
        <>
          {this.store.enableReqMod && (
            <Fieldset legend="AVISO">
              <h6 style={{ color: '#dd0303' }}>
                A EDIÇÃO DESTA DISPENSA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
              </h6>
            </Fieldset>
          )}
          <div className="p-fluid p-formgrid p-grid" style={{ alignItems: 'center' }}>
            <Divider />
            {this.store.enableReqMod && (
              <PermissionProxy resourcePermissions={'admin'} hideOnFail>
                <FormField
                  columns={{
                    sm: 12,
                    md: 4,
                    lg: 4,
                    xl: 4,
                  }}
                  label="Entidade"
                  rule={getRule('entidade')}
                  submitted={submitted}
                  attribute="entidade"
                >
                  <AutoComplete
                    value={this.store.object.entidade}
                    suggestions={this.store.entidadesFiltradas}
                    completeMethod={(e) => this.store.getEntidadesFiltradas(e, () => this.forceUpdate())}
                    field="nome"
                    onChange={(e) => this.store.updateAttribute('entidade', e.value)}
                    aria-label="Entidade"
                    dropdownAriaLabel="Selecione a entidade"
                  />
                </FormField>
              </PermissionProxy>
            )}
            {!this.store.object?.processoMigrado && (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="lei"
                label="A dispensa será regida por qual legislação?"
                rule={getRule('lei')}
                submitted={submitted}
              >
                <SelectButton
                  disabled={this.props.action === 'edit'}
                  optionLabel="text"
                  optionValue="value"
                  value={this.store.object.lei}
                  options={DadosEstaticosService.getTipoLicitacaoLei14133()}
                  onChange={(e) => {
                    if (e.value) {
                      if (this.store.arquivoDispensaList?.length) {
                        this.setState({ currentLesgilacao: e.value });
                        this._toggleDialogVisibility();
                      } else {
                        this.store.loadTipos(e.value, this.forceUpdate());
                        this.store.updateAttribute('lei', e.value);
                        this.store.updateAttribute('termoReferencia', null);
                        this.store.updateAttribute('legislacaoOutros', '');
                        this.store.updateAttribute('fundamentacaoLegalEntidade', undefined);
                        this.store.carregarFundamentacaoLegal(e.value, () => this.forceUpdate());
                        this.forceUpdate();
                      }
                    }
                  }}
                />
              </FormField>
            )}
            {this.state.dialogVisibility && this._renderDialog()}
            {this.store.object?.lei === 'OUTRA' && (
              <>
                <FormField
                  rule={getRule('legislacaoOutros')}
                  columns={{
                    sm: 12,
                    md: 4,
                    lg: 4,
                    xl: 4,
                  }}
                  attribute="legislacaoOutros"
                  label="Outra Lei"
                  infoTooltip="Informe sob qual lei o processo está sendo criado"
                  submitted={submitted}
                >
                  <InputText
                    disabled={this.props.action === 'edit'}
                    value={this.store.object.legislacaoOutros}
                    placeholder="Informe a legislação"
                    rows={4}
                    onChange={(e) => this.store.updateAttribute('legislacaoOutros', e)}
                  />
                </FormField>
              </>
            )}
            {!this.store.object.processoMigrado && (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="participacaoExclusiva"
                label="Destinado exclusivamente à participação de microempresas e empresas de pequeno porte?"
                rule={getRule('participacaoExclusiva')}
                submitted={submitted}
              >
                <div className="p-field-radiobutton p-dir-row">
                  <div className="p-field-radiobutton p-col mb-0">
                    <RadioButton
                      inputId="participacaoExclusiva"
                      name="participacaoExclusiva"
                      value={true}
                      onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                      checked={this.store.object.participacaoExclusiva}
                    />
                    <label htmlFor="participacaoExclusiva">Sim</label>
                  </div>
                  <div className="p-field-radiobutton p-col mb-0">
                    <RadioButton
                      inputId="semparticipacaoExclusiva"
                      name="senParticipacaoExclusiva"
                      value={false}
                      onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                      checked={this.store.object.participacaoExclusiva === false}
                    />
                    <label htmlFor="senParticipacaoExclusiva">Não</label>
                  </div>
                </div>
              </FormField>
            )}
          </div>
          <div className="p-fluid p-formgrid p-grid">
            {this.renderNumeroProcessoSei()}
            {!isMigrado && (
              <FormField
                columns={{
                  sm: 12,
                  md: 3,
                  lg: 3,
                  xl: 3,
                }}
                attribute="numeroProcesso"
                label="Número da Dispensa"
                rule={getRule('numeroProcesso')}
                submitted={submitted}
              >
                <InputText
                  maxLength={4}
                  onInput={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    updateAttribute('numeroProcesso', value);
                  }}
                  placeholder="Informe um número"
                  value={this.store.object.numeroProcesso}
                />
              </FormField>
            )}
            <FormField
              columns={
                isMigrado
                  ? {
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }
                  : {
                      sm: 12,
                      md: 3,
                      lg: 3,
                      xl: 3,
                    }
              }
              attribute="anoDispensa"
              label="Ano"
              rule={getRule('anoDispensa')}
              submitted={submitted}
            >
              <Dropdown
                onChange={(e) => this.store.updateAttribute('anoDispensa', e)}
                placeholder="Informe o ano"
                value={this.store.object?.anoDispensa}
                id="anoDispensa"
                optionLabel="text"
                optionValue="value"
                options={this.store.anos}
              />
            </FormField>
            <FormField
              columns={{
                sm: 12,
                md: 4,
                lg: 4,
                xl: 4,
              }}
              attribute="dataPedido"
              label="Data da Autorização"
              rule={getRule('dataPedido')}
              submitted={submitted}
            >
              <FcCalendar
                onChange={(e) => updateAttribute('dataPedido', e)}
                value={this.store.object.dataPedido && moment(this.store.object.dataPedido).toDate()}
                showIcon
                mask="99/99/9999"
              />
            </FormField>
            {isMigrado ? (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="gestor"
                label="Responsável pela Dispensa"
                rule={getRule('gestor')}
                submitted={submitted}
              >
                <InputText
                  keyfilter={new RegExp('[A-Z | a-z]')}
                  onChange={(e) => updateAttribute('gestor', e)}
                  placeholder="Informe o responsável"
                  value={this.store.object.gestor}
                />
              </FormField>
            ) : (
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="responsavelDispensa"
                label="Responsável pela Dispensa"
                rule={getRule('responsavelDispensa')}
                submitted={submitted}
              >
                <Dropdown
                  id="responsavelDispensa"
                  optionLabel="nome"
                  optionValue="id"
                  value={this.store.object?.idResponsavelDispensa}
                  options={this.store.responsaveisDispensa}
                  onChange={(e) => {
                    this.store.updateResponsavelDispensaAttribute('idResponsavelDispensa', e.value);
                  }}
                  placeholder="Selecione o(a) responsável"
                  emptyMessage="Não há responsáveis disponíveis"
                />
              </FormField>
            )}
            {!isMigrado && (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="fundamentacaoLegalEntidade"
                label="Fundamentação Legal"
                rule={getRule('fundamentacaoLegalEntidade')}
                submitted={submitted}
              >
                <Dropdown
                  onChange={(e) => {
                    updateAttribute('fundamentacaoLegalEntidade', e.value);
                  }}
                  value={this.store.object.fundamentacaoLegalEntidade}
                  placeholder="Selecione a fundamentação legal"
                  options={this.store.fundamentacoesLegais}
                  optionLabel="fundamentacao"
                />
              </FormField>
            )}
            {this.store.object.fundamentacaoLegalEntidade?.fundamentacao === 'Outro' && (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="fundamentacao"
                label="Fundamentação Legal"
                rule={getRule('fundamentacao')}
                submitted={submitted}
              >
                <InputText
                  onChange={(e) => updateAttribute('fundamentacao', e)}
                  placeholder="Informe a fundamentação legal"
                  value={this.store.object.fundamentacao}
                />
              </FormField>
            )}
            {this.store.object.id && this.store.object.fundamentacao && isMigrado && (
              <>
                <FormField
                  columns={{
                    sm: 12,
                    md: 4,
                    lg: 4,
                    xl: 4,
                  }}
                  attribute="fundamentacao"
                  label="Fundamentação Legal (Valor Antigo)"
                  submitted={submitted}
                >
                  <InputText value={this.store.object.fundamentacao} disabled />
                </FormField>
              </>
            )}
            <FormField
              columns={
                this.store.object.lei === 'LEI_N_8666'
                  ? {
                      sm: 12,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    }
                  : {
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }
              }
              attribute="orgaosParticipantes"
              label="Órgãos Participantes"
              rule={getRule('orgaosParticipantes')}
              submitted={submitted}
            >
              <AsyncMultiselect
                placeholder="Selecione os órgãos participantes"
                value={this.store.object?.orgaosParticipantes}
                onChange={(e) => this.store.updateAttribute('orgaosParticipantes', e)}
                store={this.store.orgaosParticipantesStore}
                label="nome"
                showOverlay
              />
            </FormField>
            {!this.store.isLegislacaoAntiga() && (
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="termoReferencia"
                label="Termo de Referência"
                rule={getRule('termoReferencia')}
                submitted={submitted}
              >
                <TermoSelectDetails
                  value={this.store.object.termoReferencia}
                  label="identificadorProcesso"
                  indexStore={this.store.termoIndexStore}
                  onChange={(e) => {
                    this.store.updateAttribute('termoReferencia', e);
                    this.store.updateAttribute('valor', undefined);
                    this.store.updateAttribute('fornecedores', []);
                  }}
                  emptyMessage="Selecione o termo"
                  dialogColumns={columnsTermo}
                  searchFields={['identificadorProcesso']}
                  filterSuggest={this.store.getFilterSuggestTermo()}
                  disabledComponent={this.store.enableReqMod}
                  radioMode
                />
              </FormField>
            )}
            {isMigrado && (
              <FormField
                columns={{
                  sm: 12,
                  md: 4,
                  lg: 4,
                  xl: 4,
                }}
                attribute="valor"
                label="Valor (R$)"
                rule={getRule('valor')}
                submitted={submitted}
              >
                <InputMonetary
                  onChange={(e) => updateAttribute('valor', e)}
                  placeholder="Informe o valor"
                  value={this.store.object.valor}
                />
              </FormField>
            )}

            <FormField
              columns={
                this.store.object.lei === 'LEI_N_8666'
                  ? {
                      sm: 12,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    }
                  : {
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }
              }
              attribute="naturezasDoObjeto"
              label="Naturezas do Objeto"
              submitted={submitted}
              rule={getRule('naturezasDoObjeto')}
            >
              <div className="p-col-12" style={{ margin: 0, padding: 0 }}>
                <FcMultiSelect
                  onChange={(e) => {
                    const naturezasSelecionadas = e?.target?.value;

                    this.store.updateAttribute('naturezasDoObjeto', e);
                    this.store.loadTipos(this.store.object.lei);
                    if (this._naturezaHasObra(naturezasSelecionadas) && !this.store.object.obra) {
                      this.store.updateAttribute('tipoSelecao', 'PONTO');
                      this.store.updateAttribute('obra', new Obra());
                      this.store.updateObraAttribute('finalizada', false);
                    } else if (!this._naturezaHasObra(naturezasSelecionadas) && this.store.object.obra) {
                      this.store.updateAttribute('obra', undefined);
                      this.store.updateAttribute('tipoObra', undefined);
                      this.store.updateAttribute('categoria', undefined);
                      this.store.updateAttribute('coordenadas', '');
                      this.store.initializeObraDTO();
                    }
                  }}
                  placeholder="Selecione as naturezas do objeto"
                  value={this.store.object.naturezasDoObjeto}
                  options={DadosEstaticosService.getNaturezaObjetoLicitacao()}
                  optionValue="value"
                  optionLabel="text"
                  filterBy="text"
                  filter
                  selectedItemsLabel="{} itens selecionados"
                  showClear
                  showOverlay
                />
              </div>
            </FormField>
            <FormField
              columns={
                isMigrado
                  ? 12
                  : this.store.object.lei === 'LEI_N_8666'
                  ? {
                      sm: 12,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    }
                  : {
                      sm: 12,
                      md: 6,
                      lg: 6,
                      xl: 6,
                    }
              }
              attribute="fontesDeRecurso"
              label="Fontes de Recurso"
              rule={getRule('fontesDeRecurso')}
              submitted={submitted}
            >
              <FcMultiSelect
                placeholder="Selecione as fontes de recurso"
                value={this.store.object.fontesDeRecurso}
                onChange={(e) => this.store.updateAttribute('fontesDeRecurso', e)}
                options={this.store.fontesRecursos}
                showOverlay
                optionLabel="nome"
                filterBy="nome"
                filter
                selectedItemsLabel="{} itens selecionados"
                showClear
              />
            </FormField>
            {this.store?.object?.naturezasDoObjeto && this._naturezaHasObra(this.store?.object?.naturezasDoObjeto) && (
              <>
                <FormField
                  columns={{
                    sm: 12,
                    md: 6,
                    lg: 6,
                    xl: 6,
                  }}
                  attribute="tipoObra"
                  label="Tipo da Obra"
                  rule={getRule('tipoObra')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={(_, v) => {
                      this.store.updateObraAttribute('tipo', v);
                      this.store.updateAttribute('tipoObra', v);
                      this.forceUpdate();
                    }}
                    value={this.store.object.obra?.tipo?.id}
                    placeholder="Selecione o tipo da obra"
                    store={this.store.obraTipoStore}
                    disabled={this.props.readOnly}
                  />
                </FormField>
                <FormField
                  columns={{
                    sm: 12,
                    md: 6,
                    lg: 6,
                    xl: 6,
                  }}
                  attribute="categoria"
                  label="Categoria da Obra"
                  rule={getRule('categoria')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={(_, v) => {
                      this.store.updateObraAttribute('categoria', v);
                      this.store.updateAttribute('categoria', v);
                      this.forceUpdate();
                    }}
                    value={this.store.object.obra?.categoria?.id}
                    placeholder="Selecione a categoria da obra"
                    store={this.store.obraCategoriaStore}
                    disabled={this.props.readOnly}
                  />
                </FormField>

                <FormField
                  columns={{
                    sm: 12,
                    md: 6,
                    lg: 6,
                    xl: 6,
                  }}
                  attribute="tipoSelecao"
                  label="Tipo Seleção"
                  rule={getRule('tipoSelecao')}
                  submitted={submitted}
                >
                  <div className="flex gap-2">
                    <FcDropdown
                      inOrder
                      {...this.validateField('tipoSelecao')}
                      onChange={(e) => this.store.updateAttribute('tipoSelecao', e)}
                      placeholder="Selecione o tipo de seleção"
                      value={this.store.object?.tipoSelecao}
                      id="tipoSelecao"
                      optionLabel="text"
                      optionValue="value"
                      disabled={this.props.readOnly}
                      options={DadosEstaticosService.getTipoSelecaoMapa()}
                      className="w-6"
                    />
                    <FcButton
                      label="Selecionar no Mapa"
                      type="button"
                      className="p-button-secondary w-6"
                      onClick={() => this._toggleDialogObras()}
                      loading={this.store.loading}
                      disabled={!this.store.object.tipoSelecao || this.props.readOnly}
                    />
                  </div>
                </FormField>

                <FormField
                  columns={{
                    sm: 12,
                    md: 6,
                    lg: 6,
                    xl: 6,
                  }}
                  attribute="coordenadas"
                  label="Coordenadas Geográficas"
                  rule={getRule('coordenadas')}
                  submitted={submitted}
                >
                  <InputTextarea
                    onChange={(e) => updateAttribute('coordenadas', e)}
                    value={this.store.object.coordenadas}
                    disabled
                    rows={4}
                  />
                </FormField>
              </>
            )}
          </div>

          <div className="p-fluid p-formgrid p-grid" style={{ alignItems: 'center' }}>
            <div className="p-grid p-col-12" style={{ margin: 0, padding: 0 }}>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="objeto"
                label="Objeto"
                rule={getRule('objeto')}
                submitted={submitted}
              >
                <FcInputTextarea
                  rows={6}
                  cols={30}
                  autoResize
                  onChange={(e) => updateAttribute('objeto', e)}
                  placeholder="Informe o objeto do processo"
                  value={this.store.object.objeto}
                />
              </FormField>
              <FormField
                columns={{
                  sm: 12,
                  md: 6,
                  lg: 6,
                  xl: 6,
                }}
                attribute="observacoes"
                label="Observações"
                rule={getRule('observacoes')}
                submitted={submitted}
              >
                <FcInputTextarea
                  rows={6}
                  cols={30}
                  autoResize
                  onChange={(e) => updateAttribute('observacoes', e)}
                  placeholder="Informe as observações do processo"
                  value={this.store.object.observacoes}
                />
              </FormField>
            </div>
            <div className="p-grid p-col-12">
              <div className="p-grid">
                <span className="p-mt-3 p-mb-2 p-ml-3">
                  <span>Arquivos</span>
                </span>
              </div>
              <div className="p-col-12">
                <MultipleFileUploader
                  store={this.store.fileStore}
                  onChangeFiles={(files) => this.store.setArquivoDispensaList(files)}
                  fileTypes={DadosEstaticosService.getTipoArquivoDispensa()}
                />
              </div>
              {this._renderDialogObras()}
            </div>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return content;
  }
}

FormDadosBasicos.propTypes = {
  store: PropTypes.any,
  submitted: PropTypes.any,
  action: PropTypes.any,
  readOnly: PropTypes.bool,
};

export default FormDadosBasicos;
