import React from 'react';
import { observer } from 'mobx-react';
import DispensaIndexStore from '~/stores/dispensa/indexStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import {
  checkExtinctEntityJurisdicionado,
  formataNomeEntidade,
  checkUserGroup,
  generateFullURL,
  getValueByKey,
  getValueDate,
  getValueMoney,
  hasPermissionProxy,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import AnulacaoRevogacaoModal from '../AnulacaoRevogacao/AnulacaoRevogacaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import DispensaFormStore from '~/stores/dispensa/formStore';
import TransferirEntidadeDialog from '../entidadeTransferida/transferirEntidadeDialog';

@observer
class DispensaListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.dispensa);
    this.store = new DispensaIndexStore();
    this.formStore = new DispensaFormStore();

    this.state = {
      idRemove: null,
      selectedRow: null,
      showRequisicaoRemocao: false,
      showEntidadeDialog: false,
      showAnularRevogar: false,
      showDialogTransferirEntidade: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._closeAnularRevogarModal = this._closeAnularRevogarModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
    this._handleTransferenciaEntidade = this._handleTransferenciaEntidade.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (dispensa) => this.props.onDetail(dispensa));
  }

  _handleRemocaoRequisicaoModal(dispensa) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: dispensa });
  }

  _handleAnularRevogarModal(dispensa) {
    this.setState({ showAnularRevogar: true, selectedRow: dispensa });
  }

  _handleTransferenciaEntidade(dispensa) {
    this.setState({ showDialogTransferirEntidade: true, selectedRow: dispensa });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _closeAnularRevogarModal() {
    this.setState({ showAnularRevogar: false, selectedRow: null });
  }

  _closeDialogTransferirEntidade() {
    this.setState({ showDialogTransferirEntidade: false, selectedRow: undefined });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="dispensa"
      />
    );
  }

  _renderDialogAnularRevogar() {
    return (
      <AnulacaoRevogacaoModal
        showAnularRevogar={this.state.showAnularRevogar}
        closeDialog={this._closeAnularRevogarModal}
        selectedRow={this.state.selectedRow}
        getWritePermission={this.getWritePermission}
        tipoProcessoAssociado="DISPENSA"
        updateTable={this._updateDatatable}
      />
    );
  }

  toggleDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={<div className="pl-2">{message}</div>}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.toggleDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.dispensa.novo);
        }}
      />
    );
  }

  _renderDialogTransferirEntidade() {
    return (
      <TransferirEntidadeDialog
        value={this.store.entidadeDestino}
        processoStore={this.formStore}
        radioMode
        visible={this.state.showDialogTransferirEntidade}
        closeDialog={() => this._closeDialogTransferirEntidade()}
        onCancel={() => this._closeDialogTransferirEntidade()}
        updateTable={this._updateDatatable}
        processo={this.state.selectedRow}
      />
    );
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const isAdministrador = checkUserGroup('Administrador');
    const isTransferido = cardData?.transferido;

    const items = [];

    if (
      (cardData.idRequisicaoModificacao || cardData.anulacaoRevogacao?.idRequisicaoModificacao) &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      const idRequisicaoDispensa =
        cardData.idRequisicaoModificacao ?? cardData.anulacaoRevogacao?.idRequisicaoModificacao;

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoDispensa)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoDispensa)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }

      if (isAdministrador) {
        items.push({
          label: 'Transferir Entidade',
          icon: 'pi pi-directions',
          command: () => this._handleTransferenciaEntidade(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission()) && !isTransferido) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          disabled: checkExtinctEntityJurisdicionado(),
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.dispensa.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.dispensa.editar.replace(':id', cardData.id)),
        });
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
          disabled: checkExtinctEntityJurisdicionado(),
        });
        items.push({
          label: 'Anular/Revogar',
          icon: 'pi pi-times',
          command: () => this._handleAnularRevogarModal(cardData),
          disabled: checkExtinctEntityJurisdicionado(),
        });
      }
    }

    return items;
  }

  _getDispensaTitle(cardData) {
    const numeroProcesso = cardData?.numeroProcesso;
    const ano = cardData?.anoDispensa;
    return numeroProcesso && numeroProcesso.includes('/') ? numeroProcesso : `${numeroProcesso}/${ano}`;
  }

  render() {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');

    const fields = [
      {
        label: 'Número do Processo',
        field: 'numeroProcesso',
        value: 'title',
        sortable: true,
        body: (cardData) => `Dispensa ${this._getDispensaTitle(cardData)}`,
      },
      {
        field: 'hasModificationRequest',
        label: 'Requisição de Modificação',
        value: 'hasModificationRequest',
        body: (cardData) => cardData.idRequisicaoModificacao,
      },
      {
        field: 'entidade',
        label: 'Entidade',
        value: 'subtitle',
        sortable: true,
        body: (cardData) => cardData?.entidade?.nome ?? 'Não foi possível identificar nome da entidade',
      },
      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        field: 'dataPedido',
        label: 'Data da Autorização',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-calendar',
        sortable: true,
        body: (cardData) => {
          return getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT);
        },
      },
      {
        field: 'numeroProcesso',
        label: 'Número do Processo Administrativo',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-tag',
        sortable: false,
        body: (cardData) => isJurisdicionado && (cardData?.numeroProcessoSEI ?? null),
      },
      {
        field: 'valor',
        label: 'Valor',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: (cardData) => getValueMoney(cardData.valor, cardData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
        sortable: true,
      },
      {
        label: ({ entidadeDestino }) => `Transferido para ${formataNomeEntidade(entidadeDestino.nome)}`,
        field: 'transferido',
        value: 'iconLabel',
        color: '#ad0a0a',
        icon: 'pi pi-directions',
        showIfExists: true,
        body: (cardData) => (cardData.transferido ? 'Entidade Transferida' : '-'),
      },
      {
        value: 'iconLabel',
        body: (cardData) => {
          if (isJurisdicionado) {
            return null;
          }

          if (cardData?.fornecedores) {
            const fornecedoresLabels = cardData.fornecedores.map((fornecedor) => ({
              value: fornecedor.licitante?.nome,
              toolTip: 'Fornecedor Contratado',
              icon: 'pi pi-user-plus',
              color: '#38AAAD',
            }));

            return fornecedoresLabels;
          }
        },
      },
      {
        field: 'anulacaoRevogacao',
        label: 'Anulado/Revogado',
        value: 'iconLabel',
        showIfExists: true,
        color: '#b83b20',
        icon: 'pi pi-money-bill',
        body: ({ anulacaoRevogacao }) =>
          getValueByKey(anulacaoRevogacao?.tipoOcorrencia, DadosEstaticosService.getValueAnuladoRevogado()),
        sortable: true,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        field: isJurisdicionado ? 'numeroProcesso' : 'entidade.nome',
        header: isJurisdicionado ? 'Número do Processo' : 'Entidade',
      },
      {
        field: !isJurisdicionado && 'numeroProcesso',
        header: !isJurisdicionado && 'Número do Processo',
      },
      {
        field: 'objeto',
        header: 'Objeto',
      },
      {
        field: 'dataPedido',
        header: 'Data da Autorização',
        body: (cardData) => getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valor',
        header: 'Valor',
      },
    ];

    const header = () => (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            disabled={checkExtinctEntityJurisdicionado()}
            onClick={() => this.toggleDialogEntidadeVisibility()}
          />
        </PermissionProxy>

        {this.renderTableDataExport(exportFields, 'dispensas', true)}
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroDispensa', 'gestor', 'objeto']}
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />

          <CardList
            fields={fields}
            store={this.store}
            header={header()}
            labelsLimit={4}
            onTitleClick={(cardData) => {
              if (hasPermissionProxy(this.getReadPermission())) {
                return {
                  command: () => this.props.onDetail(cardData),
                };
              }
            }}
          />

          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this.state.showRequisicaoRemocao && this._renderDialogRequisicaoRemocao()}
          {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
          {this.state.showAnularRevogar && this._renderDialogAnularRevogar()}
          {this.state.showDialogTransferirEntidade && this._renderDialogTransferirEntidade()}
        </>
      </PermissionProxy>
    );
  }
}

export default DispensaListagemPage;
