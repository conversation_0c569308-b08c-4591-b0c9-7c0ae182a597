import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Medicao extends DomainBase {
  @observable dataInicio;
  @observable dataFim;
  @observable valor;
  @observable percentualConclusao;
  @observable empenho;
  @observable valorContratoAlterado;

  static getDomainAttributes() {
    return ['empenho', 'percentualConclusao', 'valor', 'dataInicio', 'dataFim', 'valorContratoAlterado'];
  }
}

export default Medicao;
