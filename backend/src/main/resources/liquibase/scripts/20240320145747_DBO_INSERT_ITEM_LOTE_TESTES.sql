-- INSERE O TERMO DE REFERENCI<PERSON> PARA PROCESSOS LICITATORIOS
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO, TIPO, OBRA_ENGENHARIA)
VALUES (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           '2024-02-06 19:28:54.043',
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 5',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0,
           NULL,
           NULL
       ),
       (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           '2024-02-06 19:28:54.043',
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 6',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0,
           NULL,
           NULL
       );

-- INSERE O LOTE
    INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 5'),
    'LOTE PL 5.1',
    1
    ),
    ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 6'),
    'LOTE PL 6.1',
    1
    );

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 5.1' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    ),
    (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 6.1' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    );

