-- INSERE O TERMO DE REFERENCIA
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'Termo inserido para testes processos 1',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       )

-- INSERE O LOTE
    INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 1'),
    'LOTE 1',
    0
    )

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 1' AND GERADO = 0 ),
    100,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    )

-- INSERE LICITACAO
INSERT INTO LICON_DEV.dbo.LICITACAO (
	ANO_LICITACAO,
	DATA_ABERTURA,
	DATA_CADASTRO_PUBLICACAO,
	DATA_CADASTRO_VENCEDORES,
	OBJETO_LICITACAO,
	ID_STATUS_LICITACAO,
	ID_ENTIDADE,
	ID_USUARIO,
	VALOR_ESTIMADO,
	NUMERO_LICITACAO,
	EM_ANALISE,
	FASE,
	ID_TERMO_REFERENCIA,
	ID_MODALIDADE_LICITACAO_NOVA,
	LEI,
	DATA_CADASTRO_PREPARATORIA,
	VALOR_RISCO
) VALUES (
	2023,
	GETDATE(),
	GETDATE(),
	GETDATE(),
	'TESTE',
	'PUBLICADA',
	(SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
	(SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
	100.00,
	'1111234567',
	0,
	'APRESENTACAO_PROPOSTAS_LANCES',
	(SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 1'),
	(SELECT ID_MODALIDADE_LICITACAO  FROM MODALIDADE_LICITACAO WHERE NOME = 'Concurso' AND VIGENCIA_DE = '2023-04-01 00:00:00.000'),
	'LEI_N_14133',
	GETDATE(),
	12
);

-- INSERE O FORNECEDOR A LICITACAO
INSERT INTO dbo.VENCEDOR_LICITACAO  (ID_LICITACAO, ID_LICITANTE, ID_LOTE, VALOR)
VALUES(
          (SELECT ID_LICITACAO  FROM dbo.LICITACAO  WHERE NUMERO_LICITACAO = '1111234567'),
          (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28'),
          (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 1' AND GERADO = 0 ),
          1
)

-- INSERE OS ITENS LICITANTES AO VENCEDOR
    INSERT INTO dbo.ITEM(VALOR_UNITARIO, VALOR_TOTAL, QUANTIDADE, ID_TERMO_REFERENCIA_ITEM_CATALOGO, ID_VENCEDOR , DESCONTO, PREENCHIDO)
VALUES(
    1,
    100,
    100,
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 1'),
    (SELECT ID_VENCEDOR  FROM dbo.VENCEDOR_LICITACAO WHERE ID_LICITACAO = (SELECT ID_LICITACAO  FROM dbo.LICITACAO  WHERE NUMERO_LICITACAO = '1111234567') AND ID_LICITANTE = (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28')),
    0,
    1
)

