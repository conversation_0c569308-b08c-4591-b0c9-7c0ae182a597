-- INSERE O LOTE
INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
    VALUES (
       (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 1'),
       'LOTE PL 1.1',
       1
       ),
       ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 2'),
        'LOTE PL 2.1',
        1
       ),
       ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 3'),
        'LOTE PL 3.1',
        1
       ),
       ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 4'),
        'LOTE PL 4.1',
        1
       )

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
    VALUES (
        (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 1.1' AND GERADO = 1 ),
        1,
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        GETDATE(),
        (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '373719'),
        1
        ),
        (
        (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 2.1' AND GERADO = 1 ),
        1,
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        GETDATE(),
        (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '373719'),
        1
        ),
        (
        (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 3.1' AND GERADO = 1 ),
        1,
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        GETDATE(),
        (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '373719'),
        1
        ),
        (
        (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 4.1' AND GERADO = 1 ),
        1,
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        GETDATE(),
        (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '373719'),
        1
        )
