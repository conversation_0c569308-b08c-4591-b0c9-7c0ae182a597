INSERT INTO DBO.USUARIO
([login], NO<PERSON>, senha, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MATRICULA, <PERSON>UNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('diretor.dafo', 'diretor.dafo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('DAFO', 'Grupo destinado ao diretor(a) da dafo', NULL);

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'DAFO'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'diretor.dafo'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'DAFO'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'DAFO'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'analisar_alerta_dafo'));