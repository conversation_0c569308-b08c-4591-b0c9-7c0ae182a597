-- INSERE O TERMO DE REFERENCIA PARA PROCESSOS LICITATORIOS
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 1',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       ),
       (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 2',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       ),
       (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 3',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       ),
       (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 4',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       )

-- INSERE O LOTE
    INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 1'),
    'LOTE PL 1',
    1
    ),
    ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 2'),
    'LOTE PL 2',
    1
    ),
    ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 3'),
    'LOTE PL 3',
    1
    ),
    ((SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'TERMO INSERIDO PARA PROCESSOS LICITATORIOS 4'),
    'LOTE PL 4',
    1
    )

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 1' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    ),
    (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 2' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    ),
    (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 3' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    ),
    (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE PL 4' AND GERADO = 1 ),
    1,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    )

