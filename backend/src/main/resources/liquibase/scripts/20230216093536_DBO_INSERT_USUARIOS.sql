INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, <PERSON>F, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev1', 'DEV1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev2', 'DEV2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, OR<PERSON>EM, <PERSON><PERSON><PERSON>AD<PERSON>, ATRIBUIDOR)
VALUES('dev3', 'DEV3', NULL, NULL, NULL, NULL, NULL, NU<PERSON>, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev4', 'DEV4', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev5', 'DEV5', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev6', 'DEV6', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev7', 'DEV7', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev8', 'DEV8', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev9', 'DEV9', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev10', 'DEV10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev11', 'DEV11', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev12', 'DEV12', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev13', 'DEV13', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev14', 'DEV14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev15', 'DEV15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.USUARIO
([login], NOME, senha, ORGAO, CPF, MATRICULA, FUNCAO, TELEFONE, ID_GRUPO_LICON, ORIGEM, CANCELADO, ATRIBUIDOR)
VALUES('dev16', 'DEV16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LICON', 0, 0);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev1', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev2', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev3', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev4', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev5', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev6', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev7', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev8', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev9', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev10', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev11', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev12', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev13', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev14', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev15', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO
(NOME_GRUPO, DESC_GRUPO, NUMERO_PARTICIPANTES)
VALUES('Dev16', 'Grupo destinado a desenvolvedores', NULL);

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev1'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV1'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev2'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV2'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev3'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV3'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev4'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV4'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev5'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV5'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev6'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV6'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev7'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV7'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev8'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV8'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev9'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV9'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev10'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV10'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev11'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV11'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev12'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV12'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev13'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV13'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev14'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV14'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev15'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV15'));

INSERT INTO DBO.S_GRUPO_USUARIO
(ID_GRUPO, ID_USUARIO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev16'), (SELECT ID_USUARIO FROM USUARIO WHERE NOME = 'DEV16'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev1'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev2'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev3'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev4'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev5'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev6'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev7'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev8'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev9'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev10'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev11'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev12'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev13'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev14'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev15'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));

INSERT INTO DBO.S_GRUPO_PERMISSOES
(ID_GRUPO, ID_PERMISSAO)
VALUES((SELECT ID_GRUPO FROM S_GRUPO WHERE NOME_GRUPO = 'Dev16'), (SELECT ID_PERMISSAO FROM S_PERMISSAO sp WHERE NOME_PERMISSAO = 'entrada'));