-- INSERE O TERMO DE REFERENCIA
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES (
	(SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
	(SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
	GETDATE(),
	'Termo inserido para testes processos 2',
	0,
	0,
	0,
	'PREENCHIMENTO_MANUAL',
	NULL,
	0
)

-- INSERE O LOTE
INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
	(SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 2'),
	'LOTE 2',
	0
)

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
	(SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 2' AND GERADO = 0 ),
	100,
	(SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
	GETDATE(),
	(SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
	1
)

-- INSERE A CARONA
INSERT INTO dbo.CARONA
	(NUMERO_PROCESSO_ADMINISTRATIVO,
	DATA_ADESAO,
	ID_NATUREZA_OBJETO,
	NUM_PROCESSO_GERENCIADOR_ATA,
	DATA_VALIDADE_ATA,
	ID_ENTIDADE,
	ID_USUARIO,
	NM_STATUS_LICITACAO,
	DATA_CADASTRO,
	VALOR_CARONA,
	RESPONSAVEL_ADESAO,
	OBJETO,
	NUMERO_DOWNLOADS,
	ID_TERMO_REFERENCIA,
	ORIGEM_LICON_2,
	PROCESSO_ENTIDADE_ORIGEM,
	ID_ENTIDADE_ORIGEM,
	ID_LICITACAO,
	VALOR_RISCO	)
VALUES
	(
	'1121234567',
	GETDATE(),
	'COMPRAS',
	'0003/2023',
	GETDATE(),
	(SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
	(SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
	'NAO_PUBLICADA',
	GETDATE(),
	100.0,
	'ANTONIO LIRA DE MORAIS',
	(SELECT OBJETO_LICITACAO  FROM dbo.LICITACAO l WHERE NUMERO_PROCESSO_ADMINISTRATIVO  = '892/2023'),
	0,
	(SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 2'),
	1,
	1,
	(SELECT ID_ENTIDADE  FROM dbo.ENTIDADE e WHERE NOME = 'CAMARA MUNICIPAL DE RIO BRANCO'),
	(SELECT ID_LICITACAO  FROM dbo.LICITACAO l  WHERE NUMERO_PROCESSO_ADMINISTRATIVO  = '892/2023'),
	1);

-- INSERE O FORNECEDOR A CARONA
INSERT INTO dbo.CARONA_LICITANTE (ID_CARONA, ID_LICITANTE, ID_LOTE, VALOR)
VALUES(
	(SELECT ID_CARONA FROM dbo.CARONA WHERE NUMERO_PROCESSO_ADMINISTRATIVO = '1121234567'),
	(SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28'),
	(SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 2' AND GERADO = 0 ),
	1
)

-- INSERE OS ITENS LICITANTES AO FORNECEDOR
INSERT INTO dbo.ITEM(VALOR_UNITARIO, VALOR_TOTAL, QUANTIDADE, ID_TERMO_REFERENCIA_ITEM_CATALOGO, ID_DETENTOR, DESCONTO, PREENCHIDO)
VALUES(
	1,
	100,
	100,
	(SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 2'),
	(SELECT ID_CARONA_LICITANTE FROM dbo.CARONA_LICITANTE cl WHERE ID_CARONA = (SELECT ID_CARONA FROM dbo.CARONA WHERE NUMERO_PROCESSO_ADMINISTRATIVO = '1121234567') AND ID_LICITANTE = (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28')),
	0,
	1
)