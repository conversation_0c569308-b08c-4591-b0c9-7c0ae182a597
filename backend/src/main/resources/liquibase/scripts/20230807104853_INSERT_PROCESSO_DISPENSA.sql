-- INSERE O TERMO DE REFERENCIA
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES (
           (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
           (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
           GETDATE(),
           'Termo inserido para testes processos 3',
           0,
           0,
           0,
           'PREENCHIMENTO_MANUAL',
           NULL,
           0
       )

-- INSERE O LOTE
INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 3'),
    'LOTE 3',
    0
    )

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 3' AND GERADO = 0 ),
    100,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    )

INSERT INTO LICON_DEV.dbo.DISPENSA (
    DATA_PEDIDO,
    NUMERO_PROCESSO,
    OBJETO,
    ID_ENTIDADE,
    ID_USUARIO,
    NM_STATUS_LICITACAO,
    DATA_CADASTRO,
    VALOR,
    RESPONSAVEL,
    ORIGEM_LICON_2,
    ID_TERMO_REFERENCIA,
    ID_FUNDAMENTACAO_LEGAL,
    NUMERO_PROCESSO_SEI
)
VALUES(
    GETDATE(),
    '1131234567',
    'TESTE',
    (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    'PUBLICADA',
    GETDATE(),
    100.0,
    'Glauber Feitoza Maia',
    1,
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 3'),
    (SELECT ID_FUNDAMENTACAO_LEGAL  FROM dbo.FUNDAMENTACAO_LEGAL fl WHERE FUNDAMENTACAO  = 'Art. 24 - I'),
    '113123456'
    );

-- INSERE O FORNECEDOR A DISPENSA
INSERT INTO dbo.DISPENSA_LICITANTE  (ID_DISPENSA, ID_LICITANTE, ID_LOTE, VALOR)
VALUES(
          (SELECT ID_DISPENSA  FROM dbo.DISPENSA  WHERE NUMERO_PROCESSO = '1131234567'),
          (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28'),
          (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 3' AND GERADO = 0 ),
          1
      )

-- INSERE OS ITENS LICITANTES AO FORNECEDOR
INSERT INTO dbo.ITEM(VALOR_UNITARIO, VALOR_TOTAL, QUANTIDADE, ID_TERMO_REFERENCIA_ITEM_CATALOGO, ID_FORNECEDOR, DESCONTO, PREENCHIDO)
VALUES(
    1,
    100,
    100,
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 3'),
    (SELECT ID_FORNECEDOR  FROM dbo.DISPENSA_LICITANTE  cl WHERE ID_DISPENSA = (SELECT ID_DISPENSA FROM dbo.DISPENSA WHERE NUMERO_PROCESSO = '1131234567') AND ID_LICITANTE = (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28')),
    0,
    1
    )