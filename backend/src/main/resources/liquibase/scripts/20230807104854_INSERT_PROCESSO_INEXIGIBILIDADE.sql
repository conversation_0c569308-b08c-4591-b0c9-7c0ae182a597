-- INSERE O TERMO DE REFERENCIA
INSERT INTO dbo.TERMO_REFERENCIA(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS, FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES (
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
    GETDATE(),
    'Termo inserido para testes processos 4',
    0,
    0,
    0,
    'PREENCHIMENTO_MANUAL',
    NULL,
    0
)

-- INSERE O LOTE
INSERT INTO dbo.LOTE( ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES (
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 4'),
    'LOTE 4',
    0
    )

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO, VALOR_UNITARIO_ESTIMADO)
VALUES (
    (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 4' AND GERADO = 0 ),
    100,
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
    GETDATE(),
    (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL =  '600581'),
    1
    )

-- INSERE INEXIGIBILIDADE
INSERT INTO dbo.INEXIGIBILIDADE (
    DATA_PEDIDO,
    NUMERO_PROCESSO,
    OBJETO,
    ID_ENTIDADE,
    ID_USUARIO,
    NM_STATUS_LICITACAO,
    DATA_CADASTRO,
    VALOR,
    RESPONSAVEL_RATIFICACAO,
    NUMERO_SEI,
    ID_FUNDAMENTACAO_LEGAL,
    ID_TERMO_REFERENCIA
)
VALUES(
	GETDATE(),
	'1141234567',
	'TESTE',
	 (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'PREFEITURA MUNICIPAL DE TARAUACA'),
    (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
	'PUBLICADA',
	GETDATE(),
	100.00,
	'RESPONSAVEL PELA RATIFICACAO',
	'1141234567',
	(SELECT ID_FUNDAMENTACAO_LEGAL  FROM dbo.FUNDAMENTACAO_LEGAL fl WHERE FUNDAMENTACAO  = 'Art. 24 - I'),
	(SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 4')
);

-- INSERE O FORNECEDOR A INEXIGIBILIDADE
INSERT INTO dbo.INEXIGIBILIDADE_LICITANTE  (ID_INEXIGIBILIDADE, ID_LICITANTE, ID_LOTE, VALOR)
VALUES(
  (SELECT ID_INEXIGIBILIDADE  FROM dbo.INEXIGIBILIDADE  WHERE NUMERO_PROCESSO = '1141234567'),
  (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28'),
  (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE 4' AND GERADO = 0 ),
  1
)

-- INSERE OS ITENS LICITANTES AO FORNECEDOR
INSERT INTO dbo.ITEM(VALOR_UNITARIO, VALOR_TOTAL, QUANTIDADE, ID_TERMO_REFERENCIA_ITEM_CATALOGO, ID_FORNECEDOR_INEXIGIBILIDADE , DESCONTO, PREENCHIDO)
VALUES(
    1,
    100,
    100,
    (SELECT ID_TERMO_REFERENCIA  FROM dbo.TERMO_REFERENCIA  WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processos 4'),
    (SELECT ID_INEXIGIBILIDADE_LICITANTE  FROM dbo.INEXIGIBILIDADE_LICITANTE WHERE ID_INEXIGIBILIDADE = (SELECT ID_INEXIGIBILIDADE  FROM dbo.INEXIGIBILIDADE  WHERE NUMERO_PROCESSO = '1141234567') AND ID_LICITANTE = (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28')),
    0,
    1
)