-- INSERE O TERMO DE REFERENCIA
INSERT INTO dbo.TERMO_REFERENCIA
(ID_USUARIO, ID_ENTIDADE, DATA_CADASTRO, IDENTIFICADOR_PROCESSO, FINALIZADO, SRP, TRES_CASAS_DECIMAIS,
 FORMA_PREENCHIMENTO_SECAO, ID_REQUISICAO_MODIFICACAO, MODELO)
VALUES ((SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'CÂMARA MUNICIPAL DE RIO BRANCO '),
        GETDATE(),
        'Termo inserido para testes processo carona',
        1,
        1,
        0,
        'PREENCHIMENTO_MANUAL',
        NULL,
        0);

-- INSERE O LOTE
INSERT INTO dbo.LOTE(ID_TERMO_REFERENCIA, NOME, GERADO)
VALUES ((SELECT ID_TERMO_REFERENCIA
         FROM dbo.TERMO_REFERENCIA
         WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processo carona'
           AND ID_ENTIDADE = (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'CÂMARA MUNICIPAL DE RIO BRANCO ')),
        'LOTE CARONA',
        0);

-- INSERE O ITEM AO TERMO DE REFERENCIA
INSERT INTO dbo.ITEM_LOTE(ID_LOTE, QUANTIDADE, ID_USUARIO, DATA_CADASTRO, ID_MATERIAL_DETALHAMENTO,
                          VALOR_UNITARIO_ESTIMADO)
VALUES ((SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE CARONA' AND GERADO = 0),
        100,
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        GETDATE(),
        (SELECT ID_MATERIAL FROM CATALOGO.MATERIAIS WHERE CODIGO_MATERIAL = '600581'),
        1);

-- INSERE LICITACAO
INSERT INTO LICON_DEV.dbo.LICITACAO (ANO_LICITACAO,
                                     DATA_ABERTURA,
                                     DATA_CADASTRO_PUBLICACAO,
                                     DATA_CADASTRO_VENCEDORES,
                                     OBJETO_LICITACAO,
                                     ID_STATUS_LICITACAO,
                                     ID_ENTIDADE,
                                     ID_USUARIO,
                                     VALOR_ESTIMADO,
                                     NUMERO_LICITACAO,
                                     EM_ANALISE,
                                     FASE,
                                     ID_TERMO_REFERENCIA,
                                     ID_MODALIDADE_LICITACAO_NOVA,
                                     LEI,
                                     DATA_CADASTRO_PREPARATORIA,
                                     VALOR_RISCO)
VALUES (2024,
        GETDATE(),
        GETDATE(),
        GETDATE(),
        'TESTE',
        'PUBLICADA',
        (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'CÂMARA MUNICIPAL DE RIO BRANCO '),
        (SELECT ID_USUARIO FROM dbo.USUARIO u WHERE login = '99999999998' AND NOME = 'TESTE APC 02'),
        100.00,
        '123456789',
        0,
        'FINALIZACAO',
        (SELECT ID_TERMO_REFERENCIA
         FROM dbo.TERMO_REFERENCIA
         WHERE IDENTIFICADOR_PROCESSO = 'Termo inserido para testes processo carona'),
        (SELECT ID_MODALIDADE_LICITACAO
         FROM MODALIDADE_LICITACAO
         WHERE NOME = 'Concurso'
           AND VIGENCIA_DE = '2023-04-01 00:00:00.000'),
        'LEI_N_14133',
        GETDATE(),
        12);

-- INSERE O FORNECEDOR A LICITACAO
INSERT INTO dbo.VENCEDOR_LICITACAO (ID_LICITACAO, ID_LICITANTE, VALOR, ID_ITEM_LOTE, VALOR_UNITARIO, QUANTIDADE,
                                    PREENCHIDO)
VALUES ((SELECT ID_LICITACAO
         FROM dbo.LICITACAO
         WHERE NUMERO_LICITACAO = '123456789'
           AND ID_ENTIDADE = (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'CÂMARA MUNICIPAL DE RIO BRANCO ')),
        (SELECT ID_LICITANTE FROM dbo.LICITANTE WHERE CPF_CNPJ = '10.496.033/0001-28'),
        100,
        (SELECT ID_ITEM_LOTE
         FROM DBO.ITEM_LOTE
         WHERE ID_LOTE = (SELECT ID_LOTE FROM dbo.LOTE WHERE NOME = 'LOTE CARONA' AND GERADO = 0)),
        10,
        10,
        1);

-- INSERE A NATUREZA DO OBJETO
INSERT INTO DBO.LICITACAO_NATUREZA_OBJETO (ID_LICITACAO, NATUREZA_OBJETO)
VALUES ((SELECT ID_LICITACAO
         FROM dbo.LICITACAO
         WHERE NUMERO_LICITACAO = '123456789'
           AND ID_ENTIDADE = (SELECT ID_ENTIDADE FROM dbo.ENTIDADE e WHERE NOME = 'CÂMARA MUNICIPAL DE RIO BRANCO ')),
        'COMPRAS')
