package br.gov.ac.tce.licon.repositories.geoobras;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class GeoobraObraRepositoryCustomImpl
        implements GeoobraObraRepositoryCustom {

    @Autowired
    private JdbcTemplate jdbc;

    public List<Map<String,Object>> findDiferencasAuditoriaById(Long idAudt, Long idObra) {
        String sql = "select top 2 * from AUDITORIA.GEOOBRAS_OBRA_AUD where ID_AUDT <= ? AND ID_OBRA = ? order by ID_AUDT desc";
        return jdbc.queryForList(sql, idAudt, idObra);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaClassificacaoById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRAS_CLASSIFICACAO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaConvenioById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRAS_CONVENIO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaEnderecoById(Long idAudt) {
        String sql = "select * from AUDITORIA.ENDERECO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaRecursosPropriosById(Long idAudt) {
        String sql = "select * from AUDITORIA.RECURSO_PROPRIO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaMedicoesById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRAS_MEDICAO_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaDiariosById(Long idAudt) {
        String sql = "select * from AUDITORIA.DIARIO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaSituacoesById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRAS_SITUACAO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaArquivosMedicaoById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRA_ARQUIVO_MEDICAO_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaArquivosObraById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRA_ARQUIVO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaArquivosDiarioById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRA_ARQUIVO_DIARIO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }

    public List<Map<String, Object>> findDiferencasAuditoriaArquivosSituacaoById(Long idAudt) {
        String sql = "select * from AUDITORIA.GEOOBRA_ARQUIVO_SITUACAO_OBRA_AUD where ID_AUDT = ?";
        return jdbc.queryForList(sql, idAudt);
    }
}
