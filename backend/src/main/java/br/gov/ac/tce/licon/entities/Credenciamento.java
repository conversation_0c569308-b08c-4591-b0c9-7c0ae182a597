package br.gov.ac.tce.licon.entities;

import br.gov.ac.tce.licon.entities.enums.NaturezaObjeto;
import br.gov.ac.tce.licon.entities.enums.StatusAuditoriaLicitacao;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.TipoContratacao;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
@Table(name = "CREDENCIAMENTO")
@AttributeOverride(name = "id", column = @Column(name = "ID_CREDENCIAMENTO"))
public class Credenciamento extends AbstractRequisicaoModificacaoIdentificavel implements LogicallyRemovable {

    @ManyToOne(targetEntity = Entidade.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_ENTIDADE")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Entidade entidade;

    @ManyToOne(targetEntity = Usuario.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_USUARIO")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Usuario usuario;

    @OneToOne(targetEntity = TermoReferencia.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_TERMO_REFERENCIA")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private TermoReferencia termoReferencia;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_COMISSAO_CONTRATACAO")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Comissao comissaoContratacao;

    @ElementCollection(targetClass = NaturezaObjeto.class, fetch = FetchType.LAZY)
    @CollectionTable(name = "CREDENCIAMENTO_NATUREZA_OBJETO", joinColumns = @JoinColumn(name = "ID_CREDENCIAMENTO"))
    @Column(name = "NATUREZA_OBJETO", nullable = false)
    @Enumerated(EnumType.STRING)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Set<NaturezaObjeto> naturezasDoObjeto;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinTable(name = "CREDENCIAMENTO_FONTE_RECURSO", joinColumns = @JoinColumn(
            name = "ID_CREDENCIAMENTO"), inverseJoinColumns = @JoinColumn(name = "ID_FONTE_RECURSO"))
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<FonteRecurso> fontesDeRecurso;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinTable(name = "CREDENCIAMENTO_ORGAO_PARTICIPANTE", joinColumns = @JoinColumn(name = "ID_CREDENCIAMENTO"),
            inverseJoinColumns = @JoinColumn(name = "ID_ORGAO_PARTICIPANTE"))
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<Entidade> orgaosParticipantes;

    @ManyToOne(targetEntity = Usuario.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_PRESIDENTE_COMISSAO")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Usuario presidenteComissao;

    @OneToMany(mappedBy = "credenciamento", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    @NotAudited
    private List<ArquivoCredenciamento> arquivos;

    @OneToMany(mappedBy = "itemLote", cascade = CascadeType.ALL)
    @JsonIgnore
    @NotAudited
    private List<CredenciadoItem> credenciadosItens;

    @OneToOne(targetEntity = Obra.class, fetch = FetchType.LAZY, mappedBy = "credenciamento", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    @JsonManagedReference(value = "obra_credenciamento")
    private Obra obra;

    @NotNull
    @Column(name = "LEI")
    private String lei;
    
    @Column(name = "LEGISLACAO_OUTROS")
    private String legislacaoOutros;

    @NotNull
    @Column(name = "NUMERO_PROCESSO")
    private String numeroProcesso;

    @NotNull
    @Column(name = "NUMERO")
    private String numero;

    @NotNull
    @Column(name = "ANO")
    private Integer ano;

    @NotNull
    @Column(name = "INICIO_VIGENCIA")
    private LocalDate inicioVigencia;

    @NotNull
    @Column(name = "FIM_VIGENCIA")
    private LocalDate fimVigencia;

    @NotNull
    @Column(name = "TIPO_CONTRATACAO")
    @Enumerated(EnumType.STRING)
    private TipoContratacao tipoContratacao;

    @NotNull
    @Column(name = "SITIO_DIVULGACAO")
    private String sitioDivulgacao;

    @NotNull
    @Column(name = "OBJETO")
    private String objeto;

    @NotNull
    @Column(name = "DATA_CADASTRO")
    private LocalDateTime dataCadastro;

    @Column(name = "OBSERVACOES")
    private String observacoes;

    @Column(name = "NUMERO_DOWNLOADS")
    private Long numeroDownloads;

    @NotNull
    @Column(name = "STATUS")
    @Enumerated(value = EnumType.STRING)
    private StatusLicitacao status;

    @Column(name = "STATUS_ANALISE")
    @NotAudited
    @Enumerated(value = EnumType.STRING)
    private StatusAuditoriaLicitacao statusAnalise;

    @NotAudited
    @Column(name = "EM_ANALISE")
    private Boolean emAnalise;

    @Column(name = "DATA_ANALISE")
    private LocalDateTime dataAnalise;

    @Transient
    private TdaCredenciamento tda;

    @OneToMany(mappedBy = "credenciamento", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @NotAudited
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JsonManagedReference(value = "checklists")
    private List<ChecklistCredenciamento> checklists;

    @Column(name = "STATUS_PROCESSO_ARQUIVADO")
    @Enumerated(value = EnumType.STRING)
    private StatusAuditoriaLicitacao statusProcessoArquivado;

    @Column(name = "UPDATED_AT_AUDIT")
    private LocalDateTime updatedAtAudit;

    @Column(name = "PARTICIPACAO_EXCLUSIVA")
    private Boolean participacaoExclusiva;

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;

    @OneToOne(targetEntity = AnulacaoRevogacao.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_ANULACAO_REVOGACAO")
    private AnulacaoRevogacao anulacaoRevogacao;

    @NotAudited
    @Formula(value = "(CASE WHEN CHARINDEX('/', NUMERO) > 0 THEN NUMERO ELSE NUMERO + '/' + CONVERT(VARCHAR(4), ANO) END)")
    private String numeroCredenciamento;

    @Column(name = "RESPONSAVEL_CREDENCIAMENTO")
    private String responsavelCredenciamento;

    @Column(name = "ID_RESPONSAVEL_CREDENCIAMENTO")
    private Long idResponsavelCredenciamento;

    @Column(name = "TRANSFERIDO")
    private Boolean transferido = false;

    @Column(name = "DATA_TRANSFERENCIA")
    private LocalDateTime dataTransferencia;

    @ManyToOne(targetEntity = Entidade.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_ENTIDADE_DESTINO")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Entidade entidadeDestino;

    @Override
    public String toString() {
        return "Credenciamento Nº " + this.numeroProcesso;
    }

    public String titulo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        return String.format("%s - CREDENCIAMENTO DA LICITAÇÃO %s - DATA CREDENCIAMENTO: %s", this.entidade.getNome(), this.numeroProcesso, this.dataCadastro.toLocalDate().format(formatter));
    }

}

