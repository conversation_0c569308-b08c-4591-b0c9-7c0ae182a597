package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.configuration.PaginationConfig;
import br.gov.ac.tce.licon.dtos.requests.CatalogoRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.elastic.MaterialElastic;
import br.gov.ac.tce.licon.services.CatalogoElasticService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.index.search.MatchQueryParser;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CatalogoElasticServiceImpl implements CatalogoElasticService {
    private static final float NOME_WEIGHT = 10;
    private static final float CODIGO_WEIGHT = 10;
    private static final float CARACTERISTICAS_WEIGHT = 1;
    private static final float STATUS_SUSPENSO_WEIGHT = 0.2F;

    @Value("${spring.elasticsearch.index}")
    protected String springElasticsearchIndex;

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Autowired
    protected PaginationConfig paginationConfig;

    public BuscaResponse<MaterialElastic> search(CatalogoRequest filtro) {
        String query = filtro.getSearch();

        MatchQueryBuilder nomeTextQuery = QueryBuilders.matchQuery("NOME_MATERIAL", query)
                .operator(Operator.OR)
                .boost(NOME_WEIGHT)
                .fuzziness(Fuzziness.AUTO)
                .zeroTermsQuery(MatchQueryParser.ZeroTermsQuery.ALL);

        MatchQueryBuilder caracteristicasTextQuery = QueryBuilders.matchQuery("CARACTERISTICAS_BUSCA", query)
                .operator(Operator.OR)
                .boost(CARACTERISTICAS_WEIGHT)
                .fuzziness(Fuzziness.AUTO)
                .zeroTermsQuery(MatchQueryParser.ZeroTermsQuery.ALL);

        MultiMatchQueryBuilder codigoTextQuery = QueryBuilders.multiMatchQuery(query, "CODIGO_MATERIAL", "CODIGO_PDM")
                .operator(Operator.AND)
                .boost(CODIGO_WEIGHT);

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (query.isEmpty() || Pattern.compile("[a-zA-Z]").matcher(query).find()) {
            boolQuery.should(nomeTextQuery);
            boolQuery.should(caracteristicasTextQuery);
        } else {
            boolQuery.must(codigoTextQuery);
        }

        if (filtro.getFilters() != null && !filtro.getFilters().isEmpty()) {
            filtro.getFilters().forEach((filter) -> addTermQuery(boolQuery, filter.getField(), filter.getValue()));
        }

        int pageIndex = filtro.getPage().getIndex() - 1;
        int pageSize = filtro.getPage().getSize();

        FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(boolQuery,
                new FunctionScoreQueryBuilder.FilterFunctionBuilder[]{
                        new FunctionScoreQueryBuilder.FilterFunctionBuilder(
                                QueryBuilders.termQuery("ITEM_SUSPENSO", "S"),
                                ScoreFunctionBuilders.weightFactorFunction(STATUS_SUSPENSO_WEIGHT))
                });

        Query searchQuery = new NativeSearchQueryBuilder()
                .withQuery(functionScoreQuery)
                .withPageable(PageRequest.of(pageIndex, pageSize))
                .withHighlightFields(new HighlightBuilder.Field("MATERIAL_TEXT"))
                .build();

        SearchHits<MaterialElastic> hits = elasticsearchOperations.search(searchQuery, MaterialElastic.class, IndexCoordinates.of(springElasticsearchIndex));

        BuscaResponse<MaterialElastic> response = new BuscaResponse<>();

        List<MaterialElastic> results = hits.stream().map(searchHit -> {
            MaterialElastic material = searchHit.getContent();
            float score = searchHit.getScore();
            material.setScore(score);
            return material;
        }).collect(Collectors.toList());

        response.setTotal(hits.getTotalHits());
        response.setItems(results);

        return response;
    }

    private void addTermQuery(BoolQueryBuilder boolQuery, String field, String value) {
        if (value != null && !value.isEmpty()) {
            boolQuery.filter(QueryBuilders.termQuery(field, value));
        }
    }

}
