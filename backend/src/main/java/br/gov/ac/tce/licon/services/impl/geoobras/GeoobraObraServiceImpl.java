package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoObraMedicaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoDiarioObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoGeoobraObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoMedicaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoSituacaoObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.*;
import br.gov.ac.tce.licon.dtos.responses.*;
import br.gov.ac.tce.licon.dtos.responses.cjurApi.PessoaVinculoDTO;
import br.gov.ac.tce.licon.entities.AbstractIdentificavel;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.EnumValor;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.geoobras.*;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoGeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.ClassificacaoObra;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.SituacaoObra;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.ContratoRepository;
import br.gov.ac.tce.licon.repositories.EntidadeRepository;
import br.gov.ac.tce.licon.repositories.IRepository;
import br.gov.ac.tce.licon.repositories.MunicipioRepository;
import br.gov.ac.tce.licon.repositories.geoobras.ComplexoObraRepository;
import br.gov.ac.tce.licon.repositories.geoobras.GeoobraObraRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.cjurApi.CjurApiService;
import br.gov.ac.tce.licon.services.geoobras.*;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.GeoobraObraSpecification;
import br.gov.ac.tce.licon.utils.UtilsKML;
import br.gov.ac.tce.licon.utils.UtilsRequisicaoModificacao;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.j256.simplemagic.ContentType;
import com.vividsolutions.jts.geom.Geometry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.persistence.*;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional
public class GeoobraObraServiceImpl
        extends AbstractUploadTipoServiceImpl<GeoobraObra, GeoobraObraFiltroRequest, GeoobraObraRepository, ArquivoGeoobraObraFileService, ArquivoGeoobraObra, ArquivoGeoobraObraFiltroRequest, ArquivoGeoobraObraService, ArquivoGeoobraObraDTO, ArquivoGeoobraObraToDtoMapper, TipoArquivoGeoobraObra>
        implements GeoobraObraService {

    @Autowired
    private GeoobraObraRepository repository;

    @Autowired
    private ArquivoGeoobraObraFileService arquivoGeoobraObraFileService;

    @Autowired
    private ArquivoGeoobraObraService arquivoGeoobraObraService;

    @Autowired
    private ArquivoGeoobraObraToDtoMapper arquivoGeoobraObraToDtoMapper;

    @Autowired
    private CjurApiService cjurApiService;

    @Autowired
    private ContratoRepository contratoRepository;

    @Autowired
    private EntidadeRepository entidadeRepository;

    @Autowired
    private ComplexoObraRepository complexoObraRepository;

    @Autowired
    private MunicipioRepository municipioRepository;

    @Autowired
    private ArquivoDiarioObraToDtoMapper arquivoDiarioObraToDtoMapper;

    @Autowired
    private ArquivoMedicaoToDtoMapper arquivoMedicaoToDtoMapper;

    @Autowired
    private ArquivoSituacaoObraToDtoMapper arquivoSituacaoObraToDto;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private SituacaoObraService situacaoObraService;

    @Autowired
    private ArquivoDiarioObraService arquivoDiarioObraService;

    @Autowired
    private ArquivoSituacaoObraService arquivoSituacaoObraService;

    @Autowired
    private ArquivoMedicaoService arquivoMedicaoService;

    @Autowired
    private MedicaoService medicaoService;

    @Autowired
    private DiarioObraService diarioObraService;

    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.ZIP,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    @Override
    public GeoobraObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<GeoobraObra> getSpecification(GeoobraObraFiltroRequest filtro) {
        return new GeoobraObraSpecification(filtro);
    }

    @Override
    public ArquivoGeoobraObraService getArquivoService() {
        return arquivoGeoobraObraService;
    }

    @Override
    public ArquivoGeoobraObraFileService getFileService() {
        return arquivoGeoobraObraFileService;
    }

    @Override
    public ArquivoGeoobraObraToDtoMapper getMapper() {
        return arquivoGeoobraObraToDtoMapper;
    }

    @Override
    protected ArquivoGeoobraObra getNewArquivo() {
        return new ArquivoGeoobraObra();
    }

    @Override
    protected void beforeSave(GeoobraObra entity) {
        if (entity.getId() == null) {
            entity.setFase(FaseGeoobraObra.CADASTRAL);
            entity.setStatusObra(GeoobraStatusObra.NAO_INICIADA);
            entity.setDataCadastro(LocalDate.now());
        }
    }

    public void saveObra(GeoobraObraDTO dto) throws AppException {
        GeoobraObra entity = dto.getObra();
        this.checkHasRequisicaoModificacaoObra(entity.getId());
        this.beforeSave(entity);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        GeoobraObra obra = save(entity);
        saveArquivos(arquivos, obra);
    }

    @Override
    public void validarArquivos(List<ArquivoGeoobraObraDTO> arquivos) {
        super.validarArquivos(arquivos);
        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoGeoobraObra.PROJETO_BASICO)
                && !(checkTypeXLSX(arquivo.getArquivo().getTipoArquivo())))) {
            throw new AppException("O arquivo do tipo 'Projeto Básico' deve estar no formato XLSX",
                    HttpStatus.UNPROCESSABLE_ENTITY);
        }
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.GEOOBRA_OBRA.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoGeoobraObra arquivoEntity, GeoobraObra entity, ArquivoGeoobraObraDTO arquivoUpload) {
        arquivoEntity.setObra(entity);
        if (TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(arquivoUpload.getTipo())) {
            ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivoUpload.getArquivo());
            InputStream inputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
            Geometry geom = UtilsKML.extractGeomFromKML(inputStream);
            arquivoEntity.setGeom(geom);
            arquivoEntity.setCentroid(geom.getCentroid());
        }
        if (TipoArquivoGeoobraObra.DESIGNACAO_FORMAL_GESTOR.equals(arquivoUpload.getTipo()) || TipoArquivoGeoobraObra.DESIGNACAO_FORMAL_FISCAL.equals(arquivoUpload.getTipo())) {
            arquivoEntity.setFase(arquivoUpload.getFase());
        }
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public void validarArquivos(ArquivosGeoobraObraDTO obraDTO) {
        List<ArquivoGeoobraObraDTO> arquivos = obraDTO.getArquivos();
        super.validarArquivos(arquivos);
        for (ArquivoGeoobraObraDTO arquivoDTO : arquivos) {
            if (TipoArquivoGeoobraObra.PLANILHA_CONTRATADA.equals(arquivoDTO.getTipo())) {
                if (!checkXlsMimeTypes(arquivoDTO.getArquivo().getTipoArquivo())) {
                    throw new AppException("O arquivo do tipo 'Planilha Contratada' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
                }
            } else if (TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(arquivoDTO.getTipo())) {
                getFileService().validaArquivoKML(arquivoDTO.getArquivo(), obraDTO.getEntidade(), null);
            }
        }
    }

    @Override
    public void iniciarObra(GeoobraObraDTO dto) {
        Boolean isNew = dto.getObra().getFase().equals(FaseGeoobraObra.CADASTRAL);
        GeoobraObra entity = dto.getObra();
        this.checkHasRequisicaoModificacaoObra(entity.getId());
        if (isNew) {
            entity.setFase(FaseGeoobraObra.INICIAL);
            entity.setStatusObra(GeoobraStatusObra.EM_ANDAMENTO);
        }
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        GeoobraObra obra = save(entity);
        validarArquivos(arquivos);
        List<ArquivoGeoobraObra> arquivosSalvos  = saveArquivos(arquivos, obra);
        this.atualizaTipoGeometria(arquivosSalvos, obra);

        if (isNew) {
            SituacaoObraDTO situacaoObraDTO = new SituacaoObraDTO(null,
                    new SituacaoObra(
                            StatusSituacaoObra.EXECUCAO_NORMAL,
                            null,
                            "Início da Obra",
                            null,
                            LocalDateTime.now(),
                            null,
                            false,
                            obra,
                            StatusLicitacao.PUBLICADA,
                            null
                    ), obra, null);
            situacaoObraService.criarSituacaoObra(situacaoObraDTO);
            entity.setFase(FaseGeoobraObra.MEDICAO);
            save(entity);
        }
    }

    public void atualizaTipoGeometria(List<ArquivoGeoobraObra> arquivosSalvos, GeoobraObra obra) {
        ArquivoGeoobraObra arquivoFaseInicialOuMedicao = arquivosSalvos.stream().filter(f ->
                (FaseGeoobraObra.INICIAL.equals(f.getFase()) ||  FaseGeoobraObra.MEDICAO.equals(f.getFase())) && TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(f.getTipo())
        ).findFirst().orElse(null);

        if (arquivoFaseInicialOuMedicao != null) {
            Geometry geometry = arquivoFaseInicialOuMedicao.getGeom();
            String tipoGeometrisStr = geometry.getGeometryType();
            if (geometry.getNumGeometries() > 1) {
                tipoGeometrisStr = geometry.getGeometryN(0).getGeometryType();
            }
            TipoGeometriaObra tipoGeometriaObra = TipoGeometriaObra.valueOf(tipoGeometrisStr.toUpperCase());
            obra.setTipoGeometriaObra(tipoGeometriaObra);
            save(obra);
        }
    }

    @Override
    public void entregarObra(GeoobraObraDTO dto) {
        GeoobraObra entity = dto.getObra();
        this.checkHasRequisicaoModificacaoObra(entity.getId());
        if (entity.getFase().equals(FaseGeoobraObra.FINALIZACAO)) {
            entity.setFase(FaseGeoobraObra.ENTREGA);
            entity.setStatusObra(GeoobraStatusObra.ENTREGUE);
        }
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        GeoobraObra obra = save(entity);
        saveArquivos(arquivos, obra);
    }

    public List<PessoaVinculoDTO> getGestores(Long idEntidade) {
        return this.cjurApiService.getGestores(idEntidade);
    }

    public List<PessoaVinculoDTO> getFiscais(Long idEntidade) {
        return this.cjurApiService.getFiscais(idEntidade);
    }

    public List<GeoobraHistoricoDTO> historicoObra(Long idObra) {
        List<Object[]> historico = repository.findHistoricoAuditoriaObra(idObra);
        List<Object[]> historicoRelacionamentos = repository.findHistoricoAuditoriaRelacionamentosObra(idObra);

        historicoRelacionamentos.forEach((linhaRel) -> {
            if (!historico.stream().anyMatch((linha) -> linha[0].equals(linhaRel[0]))) {
                historico.add(linhaRel);
            } else if (List.of("MEDICAO", "DIARIO", "SITUACAO").contains(linhaRel[4])) {
                historico.remove(historico.stream().filter((linha) -> linha[0].equals(linhaRel[0])).findFirst().orElse(null));
                historico.add(linhaRel);
            }
        });

        return historico.stream()
                .map((linha) -> new GeoobraHistoricoDTO((BigInteger) linha[0], Integer.class.isAssignableFrom(linha[1].getClass()) ? ((Integer) linha[1]).shortValue() : (Short) linha[1], ((Timestamp) linha[2]).toLocalDateTime(), (String) linha[3], (String) linha[4]))
                .sorted(Comparator.comparing(GeoobraHistoricoDTO::getData))
                .collect(Collectors.toList());
    }

    private Map<String, Object> removeAudtValues(Map<String, Object> object) {
        Map<String, Object> result = new LinkedHashMap<>();
        object.keySet().forEach((key) -> {
            if (!key.endsWith("MOD") && !List.of("ID_AUDT", "REVTYPE").contains(key)) {
                result.put(key, object.get(key));
            }
        });
        return result;
    }

    private GeoobraEntityChangesHistoricoDTO handleComplexEntitiesHistoricoObra(Long idAudt, List<GeoobraHistoricoDTO> historicoObra, String columnId
            , Function<Long, List<Map<String, Object>>> getAudtValuesFunction, Function<Map<String, Object>, String> toStringFunction
            , Function<Map<String, Object>, Map<String, Object>> toDtoMapperFunction) {
        List<Map<String, Object>> newValues = getAudtValuesFunction.apply(idAudt);

        List<Map<String, Object>> newValuesResult = new ArrayList<>();
        List<Map<String, Object>> deletedValuesResult = new ArrayList<>();
        List<GeoobraEntityHistoricoDTO> changedValuesResult = new ArrayList<>();

        if (!newValues.isEmpty()) {
            for (Map<String, Object> entity : newValues) {
                if (entity.get("REVTYPE").equals(Short.parseShort("0"))) {
                    entity = this.removeAudtValues(entity);
                    newValuesResult.add(Objects.isNull(toDtoMapperFunction) ? entity : toDtoMapperFunction.apply(entity));
                } else {
                    Map<String, Object> oldEntity = null;
                    for (Long idAudtHistorico : historicoObra.stream().filter((obraAud) -> obraAud.getIdAudt().longValue() < idAudt).map(GeoobraHistoricoDTO::getIdAudt).map(BigInteger::longValue).collect(Collectors.toList())) {
                        List<Map<String, Object>> oldEntities = getAudtValuesFunction.apply(idAudtHistorico);
                        Map<String, Object> finalEntity = entity;
                        oldEntity = oldEntities.stream().filter((entityAud) -> entityAud.get(columnId).equals(finalEntity.get(columnId))).findFirst().orElse(null);
                        if (!Objects.isNull(oldEntity)) break;
                    }
                    if (!Objects.isNull(oldEntity)) {
                        oldEntity = this.removeAudtValues(oldEntity);
                        if (entity.get("REVTYPE").equals(Short.parseShort("2"))) {
                            deletedValuesResult.add(Objects.isNull(toDtoMapperFunction) ? oldEntity : toDtoMapperFunction.apply(oldEntity));
                        } else {
                            entity = this.removeAudtValues(entity);

                            MapDifference<String, Object> differencesEntity = Maps.difference(oldEntity, entity);
                            List<GeoobraDetalheObraHistoricoDTO> changedEntity = differencesEntity.entriesDiffering().entrySet().stream().map((entry) ->
                                    new GeoobraDetalheObraHistoricoDTO(entry.getKey(), entry.getValue().leftValue(), entry.getValue().rightValue())
                            ).collect(Collectors.toList());
                            changedValuesResult.add(new GeoobraEntityHistoricoDTO(toStringFunction.apply(oldEntity), changedEntity, Objects.isNull(toDtoMapperFunction) ? null : toDtoMapperFunction.apply(oldEntity)));
                        }
                    }
                }
            }
        }
        return new GeoobraEntityChangesHistoricoDTO(newValuesResult, deletedValuesResult, changedValuesResult);
    }

    private IRepository<? extends AbstractIdentificavel> getRepositoryByColumn(String columnName) {
        switch (columnName) {
            case "ID_CONTRATO":
                return contratoRepository;
            case "ID_ENTIDADE":
                return entidadeRepository;
            case "ID_MUNICIPIO":
                return municipioRepository;
            case "ID_COMPLEXO_OBRA":
                return complexoObraRepository;
            default:
                return null;
        }
    }

    public GeoobraDetalheHistoricoDTO detalhesHistoricoObraByIdAudt(Long idAudt, Long idObra) {
        List<Map<String, Object>> historico = repository.findDiferencasAuditoriaById(idAudt, idObra);
        Boolean hasSimpleChanges = historico.get(0).get("ID_AUDT").equals(idAudt);

        historico.forEach((linha) -> (linha.keySet().stream().collect(Collectors.toList())).forEach((key) -> {
            if (key.endsWith("MOD") || List.of("ID_AUDT", "REVTYPE").contains(key)) {
                linha.remove(key);
            }
        }));

        if (historico.size() > 1 || !hasSimpleChanges) {
            List<GeoobraHistoricoDTO> historicoObra = this.historicoObra(idObra);
            Collections.reverse(historicoObra);

            MapDifference<String, Object> differences = Maps.difference(historico.get(0), historico.get(1));
            List<GeoobraDetalheObraHistoricoDTO> result;

            if (hasSimpleChanges) {
                result = differences.entriesDiffering().entrySet().stream().map((entry) ->
                        new GeoobraDetalheObraHistoricoDTO(entry.getKey(), entry.getValue().rightValue(), entry.getValue().leftValue())
                ).collect(Collectors.toList());
            } else {
                result = new ArrayList<>();
            }

            List<Map<String, Object>> newClassificacaoValues = repository.findDiferencasAuditoriaClassificacaoById(idAudt);
            if (!newClassificacaoValues.isEmpty()) {
                List<Map<String, Object>> oldClassificacaoValues = null;
                for (Long idAudtHistorico : historicoObra.stream().filter((obraAud) -> obraAud.getIdAudt().longValue() < idAudt).map(GeoobraHistoricoDTO::getIdAudt).map(BigInteger::longValue).collect(Collectors.toList())) {
                    oldClassificacaoValues = repository.findDiferencasAuditoriaClassificacaoById(idAudtHistorico);
                    if (!oldClassificacaoValues.isEmpty()) break;
                }

                if (!oldClassificacaoValues.isEmpty()) {
                    List<String> oldClassificacaoValuesList = oldClassificacaoValues.stream()
                            .filter((audValue) -> ((Short) audValue.get("REVTYPE")) == 0)
                            .map((audValue) -> Enum.valueOf(GeoobraClassificacaoObra.class, (String) audValue.get("CLASSIFICACAO")).getValor())
                            .collect(Collectors.toList());

                    List<String> newClassificacaoValuesList = newClassificacaoValues.stream()
                            .filter((audValue) -> ((Short) audValue.get("REVTYPE")) == 0)
                            .map((audValue) -> Enum.valueOf(GeoobraClassificacaoObra.class, (String) audValue.get("CLASSIFICACAO")).getValor())
                            .collect(Collectors.toList());

                    result.add(new GeoobraDetalheObraHistoricoDTO("CLASSIFICACAO", oldClassificacaoValuesList, newClassificacaoValuesList));
                }
            }
            GeoobraEntityChangesHistoricoDTO changesConvenios = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_CONVENIO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaConvenioById(id),
                    (object) -> object.get("NUMERO").toString(),
                    null
            );

            GeoobraEntityChangesHistoricoDTO changesRecursosProprios = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_RECURSO_PROPRIO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaRecursosPropriosById(id),
                    (object) -> object.get("ORIGEM").toString(),
                    null
            );

            GeoobraEntityChangesHistoricoDTO changesEnderecos = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_ENDERECO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaEnderecoById(id),
                    (object) -> object.get("ENDERECO").toString(),
                    null
            );

            GeoobraEntityChangesHistoricoDTO changesMedicao = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_MEDICAO",
                    (id) -> repository.findDiferencasAuditoriaMedicoesById(id),
                    (object) -> "",
                    null
            );

            GeoobraEntityChangesHistoricoDTO changesSituacao = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_SITUACAO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaSituacoesById(id),
                    (object) -> "",
                    null
            );

            GeoobraEntityChangesHistoricoDTO changesDiario = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_DIARIO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaDiariosById(id),
                    (object) -> "",
                    null
            );

            JsonMapper jsonMapper = UtilsRequisicaoModificacao.getJsonMapper();

            GeoobraEntityChangesHistoricoDTO changesArquivosDiario = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_ARQUIVO_DIARIO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaArquivosDiarioById(id),
                    (object) -> "",
                    (fileMap) -> jsonMapper.convertValue(arquivoDiarioObraToDtoMapper.map(arquivoDiarioObraService.fromColumnsMap(fileMap)), Map.class)
            );

            GeoobraEntityChangesHistoricoDTO changesArquivosMedicao = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_ARQUIVO_MEDICAO",
                    (id) -> repository.findDiferencasAuditoriaArquivosMedicaoById(id),
                    (object) -> "",
                    (fileMap) -> jsonMapper.convertValue(arquivoMedicaoToDtoMapper.map(arquivoMedicaoService.fromColumnsMap(fileMap)), Map.class)
            );

            GeoobraEntityChangesHistoricoDTO changesArquivosSituacao = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_ARQUIVO_SITUACAO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaArquivosSituacaoById(id),
                    (object) -> "",
                    (fileMap) -> jsonMapper.convertValue(arquivoSituacaoObraToDto.map(arquivoSituacaoObraService.fromColumnsMap(fileMap)), Map.class)
            );

            GeoobraEntityChangesHistoricoDTO changesArquivosObra = this.handleComplexEntitiesHistoricoObra(idAudt,
                    historicoObra,
                    "ID_ARQUIVO_OBRA",
                    (id) -> repository.findDiferencasAuditoriaArquivosObraById(id),
                    (object) -> "",
                    (fileMap) -> jsonMapper.convertValue(arquivoGeoobraObraToDtoMapper.map(arquivoGeoobraObraService.fromColumnsMap(fileMap)), Map.class)
            );

            Arrays.stream(GeoobraObra.class.getDeclaredFields()).forEach((field) -> {
                if (!Objects.isNull(field.getAnnotation(Enumerated.class)) &&
                        differences.entriesDiffering().keySet().contains(field.getAnnotation(Column.class).name()) && hasSimpleChanges) {
                    if (EnumValor.class.isAssignableFrom(field.getType())) {
                        String newValueEnum = Objects.isNull(differences.entriesDiffering().get(field.getAnnotation(Column.class).name()).leftValue()) ? null
                                : ((EnumValor) Enum.valueOf((Class<Enum>) field.getType(), (String) differences.entriesDiffering().get(field.getAnnotation(Column.class).name()).leftValue())).getValor();
                        String oldValueEnum = Objects.isNull(differences.entriesDiffering().get(field.getAnnotation(Column.class).name()).rightValue()) ? null
                                : ((EnumValor) Enum.valueOf((Class<Enum>) field.getType(), (String) differences.entriesDiffering().get(field.getAnnotation(Column.class).name()).rightValue())).getValor();

                        GeoobraDetalheObraHistoricoDTO resultDTO = result.stream().filter((resultField) -> resultField.getField().equals(field.getAnnotation(Column.class).name())).findFirst().get();
                        resultDTO.setNewValue(newValueEnum);
                        resultDTO.setOldValue(oldValueEnum);
                    }
                } else if (!Objects.isNull(field.getAnnotation(ManyToOne.class)) &&
                    differences.entriesDiffering().keySet().contains(field.getAnnotation(JoinColumn.class).name()) && hasSimpleChanges) {

                    String columnName = field.getAnnotation(JoinColumn.class).name();
                    IRepository<? extends AbstractIdentificavel> relationRepository = this.getRepositoryByColumn(columnName);
                    if (!Objects.isNull(relationRepository)) {
                        String newValueRelation = Objects.isNull(differences.entriesDiffering().get(columnName).leftValue()) ? null
                                : relationRepository.findById((Long) differences.entriesDiffering().get(columnName).leftValue()).map(Object::toString).orElse(null);
                        String oldValueRelation = Objects.isNull(differences.entriesDiffering().get(columnName).rightValue()) ? null
                                : relationRepository.findById((Long) differences.entriesDiffering().get(columnName).rightValue()).map(Object::toString).orElse(null);

                        GeoobraDetalheObraHistoricoDTO resultDTO = result.stream().filter((resultField) -> resultField.getField().equals(columnName)).findFirst().get();
                        resultDTO.setNewValue(newValueRelation);
                        resultDTO.setOldValue(oldValueRelation);
                    }
                }
            });

            return new GeoobraDetalheHistoricoDTO(result, changesConvenios, changesRecursosProprios, changesEnderecos, changesMedicao, changesSituacao, changesDiario
                    , changesArquivosDiario, changesArquivosMedicao, changesArquivosSituacao, changesArquivosObra);
        }
        return null;
    }

    @Override
    public void removerArquivo(Long idEntity, Long idArquivo) throws AppException {
        if (!userCanAcess(idEntity)) {
            throw new AppException("Permissões insuficientes para remover o arquivo selecionado!", HttpStatus.UNAUTHORIZED);
        }

        ArquivoGeoobraObra arquivo = this.getArquivoService().getById(idArquivo);
        if (!idEntity.equals(arquivo.getIdEntidade())) {
            throw new AppException(String.format("Erro! Não foi possível remover. Arquivo não pertence à/ao %s!", getEntityName()), HttpStatus.UNPROCESSABLE_ENTITY);
        }

        this.getArquivoService().remover(idArquivo);
    }

    @Override
    public Boolean hasRequisicaoModificacao(Long idObra) {
        if (idObra == null) {
            return false;
        }
        GeoobraObra obra = this.getById(idObra);
        return !Objects.isNull(idObra) && (!Objects.isNull(obra.getIdRequisicaoModificacao()) || this.diarioObraService.existeDiarioEmRequisicaoModificao(idObra)
                || this.medicaoService.existeMedicaoEmRequisicaoModificao(idObra) || this.situacaoObraService.existeSituacaoObraEmRequisicaoModificao(idObra));
    }

    public void checkHasRequisicaoModificacaoObra(Long idObra) {
        if (this.hasRequisicaoModificacao(idObra)) {
            throw new AppException("Não foi possível concluir a operação pois existe uma Requisição de Modificação para esta obra!", HttpStatus.BAD_REQUEST);
        }
    }
}
