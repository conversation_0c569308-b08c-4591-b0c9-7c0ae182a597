package br.gov.ac.tce.licon.controllers.geoobras;

import br.gov.ac.tce.licon.controllers.AbstractController;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MapaObraViewFiltroRequest;
import br.gov.ac.tce.licon.dtos.responses.FailureResponse;
import br.gov.ac.tce.licon.entities.geoobras.MapaObraView;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.MapaObraViewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "MapaViewController")
@RestController
@RequestMapping("/geo-obras/mapa")
public class MapaViewController extends AbstractController<MapaObraView, MapaObraViewFiltroRequest, MapaObraViewService> {

    @Autowired
    private MapaObraViewService service;

    @Override
    protected MapaObraViewService getService() {
        return service;
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping(value = "/mascara")
    @Operation(description = "Obtem uma entidade a partir de seu ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Response"),
            @ApiResponse(responseCode = "400", description = "Bad request",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = FailureResponse.class))),
            @ApiResponse(responseCode = "404", description = "Not found",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = FailureResponse.class))),
            @ApiResponse(responseCode = "default", description = "Unexpected error",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = FailureResponse.class))),
    })
    public ResponseEntity<String> getMascara() throws AppException {
        String mascara = this.service.getMascara();
        return ResponseEntity.ok(mascara);
    }


}
