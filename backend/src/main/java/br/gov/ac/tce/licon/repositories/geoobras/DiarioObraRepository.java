package br.gov.ac.tce.licon.repositories.geoobras;

import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.geoobras.DiarioObra;
import br.gov.ac.tce.licon.repositories.IRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface DiarioObraRepository extends IRepository<DiarioObra> {

    @Query(value = "SELECT d FROM DiarioObra d WHERE d.obra.id = ?1")
    List<DiarioObra> getByObra(Long idObra);

    Optional<DiarioObra> findTopByObraIdAndStatusNotOrderByIdDesc(Long obraId, StatusLicitacao status);

    Optional<DiarioObra> findTop1ByObraIdAndIdLessThanOrderByIdDesc(Long obraId, Long diarioId);

    Optional<DiarioObra> findTop1ByObraIdAndIdGreaterThanOrderByIdAsc(Long obraId, Long diarioId);

    Boolean existsByObraIdAndIdRequisicaoModificacaoIsNotNull(Long obraId);
}
