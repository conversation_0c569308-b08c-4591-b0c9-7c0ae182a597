package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoMedicaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MedicaoDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MedicaoFiltroRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoMedicao;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoGeometriaObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoMedicao;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.MedicaoRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.geoobras.GeoobraObraService;
import br.gov.ac.tce.licon.services.geoobras.MedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.MedicaoSpecification;
import br.gov.ac.tce.licon.utils.UtilsKML;
import com.google.common.collect.ImmutableList;
import com.j256.simplemagic.ContentType;
import com.vividsolutions.jts.geom.Geometry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class MedicaoServiceImpl extends AbstractUploadTipoServiceImpl<Medicao, MedicaoFiltroRequest, MedicaoRepository, ArquivoMedicaoFileService, ArquivoMedicao, ArquivoMedicaoFiltroRequest, ArquivoMedicaoService, ArquivoMedicaoDTO, ArquivoMedicaoToDtoMapper, TipoArquivoMedicao> implements MedicaoService {

    @Autowired
    private ArquivoMedicaoService arquivoMedicaoService;

    @Autowired
    private ArquivoMedicaoFileService arquivoMedicaoFileService;

    @Autowired
    private MedicaoRepository repository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private GeoobraObraService geoobraObraService;

    @Autowired
    private ArquivoMedicaoToDtoMapper arquivoMedicaoToDtoMapper;

    @Override
    public MedicaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<Medicao> getSpecification(MedicaoFiltroRequest filtro) {
        return new MedicaoSpecification(filtro);
    }

    @Override
    protected void beforeSave(Medicao entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDate.now());
        }
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Medicao> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Medicao medicao = entidadeOpt.get();
            validarRemover(medicao);
            getRepository().save(medicao);
        }
    }

    @Override
    public BuscaResponse<Medicao> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().isEmpty()) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    private Boolean validarValorMedicoes(GeoobraObra obra, Medicao medicaoAtual) {
        List<Medicao> medicoes = Optional.ofNullable(getRepository().getByObra(obra.getId())).orElse(Collections.emptyList());

        BigDecimal valorObra = obra.getAdministracaoDireta() ? obra.getValor() :
                Objects.isNull(obra.getContrato().getAditivo()) ? obra.getContrato().getValorGlobal() : obra.getContrato().getAditivo();
        BigDecimal valorSomaMedicoes = medicoes.stream()
                .filter(m -> !m.getId().equals(medicaoAtual.getId()))
                .map(Medicao::getValor)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal valorMinimo = BigDecimal.ZERO;
        BigDecimal valorMaximo = valorObra.subtract(valorSomaMedicoes);

        return medicaoAtual.getValor().compareTo(valorMinimo) >= 0 && medicaoAtual.getValor().compareTo(valorMaximo) <= 0;
    }

    private Boolean validarDatasMedicoes(Long obraId, Medicao medicaoAtual) {
        List<Medicao> medicoes = Optional.ofNullable(getRepository().getByObra(obraId)).orElse(new ArrayList<>());

        LocalDate minDateInicio = getMinDateInicio(medicoes, medicaoAtual, this.geoobraObraService.getById(obraId));
        LocalDate maxDateInicio = getMaxDateInicio(medicoes, medicaoAtual);

        if (minDateInicio != null && medicaoAtual.getDataInicio().isBefore(minDateInicio)) {
            return false;
        }

        return maxDateInicio == null || !medicaoAtual.getDataFim().isAfter(maxDateInicio);
    }

    private LocalDate getMinDateInicio(List<Medicao> medicoes, Medicao medicaoAtual, GeoobraObra obra) {
        if (medicoes == null || medicoes.isEmpty()) {
            return obra.getDataInicio();
        }

        if (medicaoAtual.getId() != null) {
            int idx = findMedicaoIndex(medicoes, medicaoAtual.getId());
            return idx > 0 ? medicoes.get(idx - 1).getDataFim() : obra.getDataInicio();
        } else {
            return medicoes.get(medicoes.size() - 1).getDataFim();
        }
    }

    private LocalDate getMaxDateInicio(List<Medicao> medicoes, Medicao medicaoAtual) {
        if (medicoes.size() >= 2 && medicaoAtual.getId() != null) {
            int idx = findMedicaoIndex(medicoes, medicaoAtual.getId());
            if (idx != -1 && idx < (medicoes.size() - 1)) {
                return medicoes.get(idx + 1).getDataInicio();
            }
        }
        return null;
    }

    private int findMedicaoIndex(List<Medicao> medicoes, Long medicaoId) {
        for (int i = 0; i < medicoes.size(); i++) {
            Medicao m = medicoes.get(i);
            if (m.getId() != null && m.getId().equals(medicaoId)) {
                return i;
            }
        }
        return -1;
    }

    @Transactional
    public void saveMedicao(MedicaoDTO dto) throws AppException {
        Medicao medicao = dto.getMedicao();
        geoobraObraService.checkHasRequisicaoModificacaoObra(medicao.getObra().getId());

        if (medicao.getId() != null) {
            Medicao ultimaMedicao = getUltimaMedicaoObra(medicao.getObra().getId());
            if (ultimaMedicao != null && !ultimaMedicao.getId().equals(medicao.getId())) {
                throw new AppException("Não é possível editar uma medição que não seja a última.", HttpStatus.BAD_REQUEST);
            }
        }

        List<ArquivoMedicaoDTO> arquivos = dto.getArquivosMedicao();

        GeoobraObra obra = this.geoobraObraService.getById(medicao.getObra().getId());

        boolean validacaoDatasMedicoes = this.validarDatasMedicoes(obra.getId(), medicao);
        boolean validarValorMedicoes = this.validarValorMedicoes(obra, medicao);
        if (!validacaoDatasMedicoes) {
            throw new AppException("Recarregue a página e verifique os intervalos de datas definidos.", HttpStatus.BAD_REQUEST);
        }
        if (!validarValorMedicoes) {
            throw new AppException("Recarregue a página e verifique o valor definido.", HttpStatus.BAD_REQUEST);
        }

        this.validarArquivos(dto, obra.getId());
        medicao = save(medicao);

        saveArquivos(arquivos, medicao);
    }

    @Override
    public ArquivoMedicaoService getArquivoService() {
        return arquivoMedicaoService;
    }

    @Override
    public ArquivoMedicaoFileService getFileService() {
        return arquivoMedicaoFileService;
    }

    @Override
    public ArquivoMedicaoToDtoMapper getMapper() {
        return arquivoMedicaoToDtoMapper;
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.MEDICAO.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);
        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(TipoGeometriaObra tipoGeometriaObra) {
        return this.obrigatoriedadeArquivoService.getArquivosObrigatorios("MEDICAO", ImmutableList.of("MEDICAO"))
                .stream()
                .filter(ObrigatoriedadeArquivo::getObrigatorio)
                .filter(obj -> tipoGeometriaObra.getValor().equals("Linha") || !obj.getArquivo().equals(TipoArquivoMedicao.GEORREFERENCIAMENTO.getValor()))
                .collect(Collectors.toList());
    }

    @Override
    protected ArquivoMedicao getNewArquivo() {
        return new ArquivoMedicao();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoMedicao arquivoEntity, Medicao entity, ArquivoMedicaoDTO arquivoUpload) {
        arquivoEntity.setMedicao(entity);
        if (TipoArquivoMedicao.GEORREFERENCIAMENTO.equals(arquivoUpload.getTipo())) {
            ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivoUpload.getArquivo());
            InputStream inputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
            Geometry geom = UtilsKML.extractGeomFromKML(inputStream);
            arquivoEntity.setGeom(geom);
            arquivoEntity.setCentroid(geom.getCentroid());
        }
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    public void validarArquivosTipos(List<TipoArquivoMedicao> tipos, TipoGeometriaObra tipoGeometriaObra) {
        this.verificarArquivosObrigatorios(tipos, this.getArquivosTiposObrigatorios(tipoGeometriaObra));
    }

    public void validarArquivos(List<ArquivoMedicaoDTO> arquivos, TipoGeometriaObra tipoGeometriaObra) {
        List<TipoArquivoMedicao> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.validarArquivosTipos(tiposArquivosEnviados, tipoGeometriaObra);
    }

    @Override
    public void validarArquivos(MedicaoDTO dto, Long idObra) {
        GeoobraObra obra = geoobraObraService.getById(idObra);
        validarArquivos(dto.getArquivosMedicao(), obra.getTipoGeometriaObra());

        List<ArquivoMedicaoDTO> arquivos = dto.getArquivosMedicao();

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoMedicao.NOTA_FISCAL) && !arquivo.getArquivo().getTipoArquivo().equals(ContentType.PDF.getMimeType()))) {
            throw new AppException("O arquivo do tipo 'Nota Fiscal' deve ser um PDF", HttpStatus.BAD_REQUEST);
        }

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoMedicao.GEORREFERENCIAMENTO) && !arquivo.getArquivo().getNomeOriginal().endsWith(".kml"))) {
            throw new AppException("O arquivo do tipo 'Georreferenciamento' deve ser um KML", HttpStatus.BAD_REQUEST);
        }
        for (ArquivoMedicaoDTO arquivoDTO : dto.getArquivosMedicao()) {
            if (TipoArquivoMedicao.GEORREFERENCIAMENTO.equals(arquivoDTO.getTipo())) {
                getFileService().validaArquivoKML(arquivoDTO.getArquivo(), dto.getEntidade(), obra);
            }
        }
    }

    @Override
    public List<Medicao> getByObra(Long idObra) {
        return this.repository.getByObra(idObra);
    }

    @Override
    public Medicao getUltimaMedicaoObra(Long obraId) {
        return this.repository.findTopByObraIdOrderByIdDesc(obraId).orElse(null);
    }

    @Override
    public boolean existeMedicaoEmRequisicaoModificao(Long obraId) {
        return this.repository.existsByObraIdAndIdRequisicaoModificacaoIsNotNull(obraId);
    }

    @Override
    public void removerArquivo(Long idEntity, Long idArquivo) throws AppException {
        if (!userCanAcess(idEntity)) {
            throw new AppException("Permissões insuficientes para remover o arquivo selecionado!", HttpStatus.UNAUTHORIZED);
        }

        ArquivoMedicao arquivo = this.getArquivoService().getById(idArquivo);
        if (!idEntity.equals(arquivo.getIdEntidade())) {
            throw new AppException(String.format("Erro! Não foi possível remover. Arquivo não pertence à/ao %s!", getEntityName()), HttpStatus.UNPROCESSABLE_ENTITY);
        }

        this.getArquivoService().remover(idArquivo);
    }
}
