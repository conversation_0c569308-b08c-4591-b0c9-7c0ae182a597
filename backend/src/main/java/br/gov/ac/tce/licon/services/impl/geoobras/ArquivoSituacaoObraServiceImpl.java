package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoSituacaoObraFiltroRequest;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoDiarioObra;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoSituacaoObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoDiarioObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoSituacaoObra;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoSituacaoObraRepository;
import br.gov.ac.tce.licon.repositories.geoobras.SituacaoObraRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoSituacaoObraService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoSituacaoObraSpecification;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class ArquivoSituacaoObraServiceImpl extends AbstractService<ArquivoSituacaoObra, ArquivoSituacaoObraFiltroRequest, ArquivoSituacaoObraRepository> implements ArquivoSituacaoObraService {

    @Autowired
    private ArquivoSituacaoObraRepository repository;

    @Autowired
    private SituacaoObraRepository situacaoObraRepository;

    @Override
    public ArquivoSituacaoObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoSituacaoObra> getSpecification(ArquivoSituacaoObraFiltroRequest filtro) {
        return new ArquivoSituacaoObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoSituacaoObra entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
    }

    @Override
    public List<ArquivoSituacaoObra> buscarPor(Long idEntity) {
        return repository.buscarPor(idEntity);
    }

    public ArquivoSituacaoObra fromColumnsMap(Map<String, Object> map) {
        ArquivoSituacaoObra result = new ArquivoSituacaoObra();
        result.setSituacaoObra(situacaoObraRepository.getById((Long) map.get("ID_SITUACAO_OBRA")));
        result.setDescricao((String) map.get("DESCRICAO"));
        result.setTipo(TipoArquivoSituacaoObra.valueOf((String) map.get("TIPO_ARQUIVO_SITUACAO_OBRA")));
        result.setId((Long) map.get("ID_ARQUIVO_SITUACAO_OBRA"));
        result.setNome((String) map.get("NOME"));
        result.setDataEnvio(((Timestamp) map.get("DATA_ENVIO")).toLocalDateTime());
        result.setDiretorio((String) map.get("DIRETORIO"));
        result.setTipoArquivo((String) map.get("TIPO_ARQUIVO"));
        return result;
    }
}
