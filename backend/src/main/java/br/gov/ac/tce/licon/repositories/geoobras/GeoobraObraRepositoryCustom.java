package br.gov.ac.tce.licon.repositories.geoobras;

import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

public interface GeoobraObraRepositoryCustom {
    List<Map<String,Object>> findDiferencasAuditoriaById(Long idAudt, Long idObra);

    List<Map<String, Object>> findDiferencasAuditoriaClassificacaoById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaConvenioById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaEnderecoById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaRecursosPropriosById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaMedicoesById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaDiariosById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaSituacoesById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaArquivosMedicaoById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaArquivosObraById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaArquivosDiarioById(Long idAudt);

    List<Map<String, Object>> findDiferencasAuditoriaArquivosSituacaoById(Long idAudt);
}

