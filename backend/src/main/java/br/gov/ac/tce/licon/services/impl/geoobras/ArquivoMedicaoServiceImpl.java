package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoFiltroRequest;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoMedicao;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoSituacaoObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoMedicao;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoSituacaoObra;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoMedicaoRepository;
import br.gov.ac.tce.licon.repositories.geoobras.MedicaoRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoMedicaoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class ArquivoMedicaoServiceImpl
        extends AbstractService<ArquivoMedicao, ArquivoMedicaoFiltroRequest, ArquivoMedicaoRepository>
        implements ArquivoMedicaoService {

    @Autowired
    private ArquivoMedicaoRepository repository;

    @Autowired
    private MedicaoRepository medicaoRepository;

    @Override
    public ArquivoMedicaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoMedicao> getSpecification(ArquivoMedicaoFiltroRequest filtro) {
        return new ArquivoMedicaoSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoMedicao entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
    }

    @Override
    public List<ArquivoMedicao> buscarPor(Long idMedicao) {
        return repository.buscarPor(idMedicao);
    }

    public ArquivoMedicao fromColumnsMap(Map<String, Object> map) {
        ArquivoMedicao result = new ArquivoMedicao();
        result.setMedicao(medicaoRepository.getById((Long) map.get("ID_MEDICAO")));
        result.setDescricao((String) map.get("DESCRICAO"));
        result.setTipo(TipoArquivoMedicao.valueOf((String) map.get("TIPO_ARQUIVO_MEDICAO")));
        result.setId((Long) map.get("ID_ARQUIVO_MEDICAO"));
        result.setNome((String) map.get("NOME"));
        result.setDataEnvio(((Timestamp) map.get("DATA_ENVIO")).toLocalDateTime());
        result.setDiretorio((String) map.get("DIRETORIO"));
        result.setTipoArquivo((String) map.get("TIPO_ARQUIVO"));
        return result;
    }
}
