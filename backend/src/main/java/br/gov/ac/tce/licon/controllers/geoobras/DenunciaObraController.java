package br.gov.ac.tce.licon.controllers.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.DenunciaDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.DenunciaResponseDTO;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.dtos.responses.FailureResponse;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.DenunciaService;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.websocket.server.PathParam;

@Tag(name = "Denuncia")
@RestController
@RequestMapping("/geo-obras/denuncias")
public class DenunciaObraController {

    @Autowired
    private DenunciaService service;


    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @PostMapping(value = "/advanced-search")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Response"), @ApiResponse(responseCode = "400", description = "Bad request", content = @Content(mediaType = "application/json", schema = @Schema(implementation = FailureResponse.class))), @ApiResponse(responseCode = "404", description = "Not found", content = @Content(mediaType = "application/json", schema = @Schema(implementation = FailureResponse.class))), @ApiResponse(responseCode = "default", description = "Unexpected error", content = @Content(mediaType = "application/json", schema = @Schema(implementation = FailureResponse.class))), })
    public ResponseEntity<BuscaResponse<DenunciaDTO>> getDenuncias(@RequestBody AdvancedSearchRequest filtro) throws AppException {
        DenunciaResponseDTO responseDenuncias = this.service.getDenuncias(filtro);
        BuscaResponse<DenunciaDTO> response = new BuscaResponse<>();
        response.setItems(responseDenuncias.getItems());
        response.setTotal(responseDenuncias.getTotal());
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadArquivosDenuncias(@PathParam("path") String path, @PathParam("nomeOriginal") String nomeOriginal) throws AppException {
        ArquivoDTO arquivoDTO = new ArquivoDTO();
        arquivoDTO.setLookupId(path);
        arquivoDTO.setNomeOriginal(nomeOriginal);

        ArquivoBinarioDTO arquivoBinarioDTO = service.downloadArquivoDenuncia(arquivoDTO);
        byte[] result = arquivoBinarioDTO.getBinario();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", arquivoBinarioDTO.getNomeOriginal()));

        ByteArrayResource resource = new ByteArrayResource(result);

        return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM).body(resource);
    }

}
