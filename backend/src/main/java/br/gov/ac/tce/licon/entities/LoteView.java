package br.gov.ac.tce.licon.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.*;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "LOTE_VIEW")
@AttributeOverride(name = "id", column = @Column(name = "ID_LOTE"))
public class LoteView extends AbstractIdentificavel {

    @Column(name = "NOME")
    private String nome;

    @ManyToOne(targetEntity = TermoReferenciaView.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_TERMO_REFERENCIA")
    @JsonBackReference(value = "lotes")
    private TermoReferenciaView termoReferencia;

    @OneToMany(targetEntity = ItemLoteView.class, mappedBy = "lote", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
	@JsonManagedReference(value = "itens")
    @OrderBy("numero ASC")
	private List<ItemLoteView> itens;

    @Column(name = "GERADO")
    private Boolean gerado;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LoteView)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        LoteView lote = (LoteView) o;
        return Objects.equals(nome, lote.nome) && itens.containsAll(lote.itens) && lote.itens.containsAll(itens) && Objects.equals(gerado, lote.gerado);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), nome, itens, gerado);
    }
}
