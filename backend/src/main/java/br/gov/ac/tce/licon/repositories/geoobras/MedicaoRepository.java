package br.gov.ac.tce.licon.repositories.geoobras;

import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import br.gov.ac.tce.licon.repositories.IRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface MedicaoRepository extends IRepository<Medicao> {

    @Query(value = "SELECT m FROM Medicao m " +
                    "LEFT JOIN FETCH m.empenho " +
                    "WHERE m.obra.id = ?1")
    List<Medicao> getByObra(Long idObra);

    Optional<Medicao> findTopByObraIdOrderByIdDesc(Long obraId);

    boolean existsByObraIdAndIdRequisicaoModificacaoIsNotNull(
            Long obraId
    );
}
