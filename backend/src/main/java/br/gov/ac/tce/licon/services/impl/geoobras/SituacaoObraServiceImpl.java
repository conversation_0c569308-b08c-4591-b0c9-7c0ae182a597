package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoSituacaoObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoSituacaoObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoSituacaoObraFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.SituacaoObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.SituacaoObraFiltroRequest;
import br.gov.ac.tce.licon.entities.ArquivoLicitacao;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.geoobras.*;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoSituacaoObra;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.SituacaoObra;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.GeoobraObraRepository;
import br.gov.ac.tce.licon.repositories.geoobras.SituacaoObraRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.geoobras.*;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.SituacaoObraSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
public class SituacaoObraServiceImpl extends AbstractUploadTipoServiceImpl<SituacaoObra, SituacaoObraFiltroRequest, SituacaoObraRepository, ArquivoSituacaoObraFileService, ArquivoSituacaoObra, ArquivoSituacaoObraFiltroRequest, ArquivoSituacaoObraService, ArquivoSituacaoObraDTO, ArquivoSituacaoObraToDtoMapper, TipoArquivoSituacaoObra> implements SituacaoObraService {

    @Autowired
    private GeoobraObraService obraService;

    @Autowired
    private SituacaoObraRepository repository;

    @Autowired
    private GeoobraObraRepository obraRepository;

    @Autowired
    private ArquivoSituacaoObraService arquivoSituacaoObraService;

    @Autowired
    private ArquivoSituacaoObraFileService arquivoSituacaoObraFileService;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoSituacaoObraToDtoMapper arquivoSituacaoObraToDtoMapper;

    @Override
    public SituacaoObraRepository getRepository() {
        return this.repository;
    }

    @Override
    protected Specification<SituacaoObra> getSpecification(SituacaoObraFiltroRequest filtro) {
        return new SituacaoObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(SituacaoObra situacao) {
        situacao.setDefinidoSistema(false);

        LocalDateTime now = LocalDateTime.now();

        if (situacao.getId() == null) {
            situacao.setDataCadastro(now);
            return;
        }

        getRepository()
                .findById(situacao.getId())
                .filter(existing -> !Objects.equals(existing.getStatusObra(), situacao.getStatusObra()))
                .ifPresent(existing -> situacao.setDataCadastro(now));
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.SITUACAO_OBRA.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);
        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    public void criarSituacaoObra(br.gov.ac.tce.licon.dtos.requests.geoobras.SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obraService.checkHasRequisicaoModificacaoObra(obra.getId());
        SituacaoObra situacaoObra = dto.getSituacaoObra();
        situacaoObra.setObra(obra);

        situacaoObra = save(situacaoObra);
        if (dto.getArquivos() != null) {
            List<ArquivoSituacaoObraDTO> arquivos = dto.getArquivos();
            saveArquivos(arquivos, situacaoObra);
        }

        if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.EXECUCAO_NORMAL)) {
            normalizarObra(dto);
        } else if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.ATRASADA)) {
            atrasarObra(dto);
        } else if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.PARALISADA)) {
            paralisarObra(dto);
        } else if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.INTERROMPIDA)) {
            interromperObra(dto);
        } else if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.REINICIADA)) {
            reiniciarObra(dto);
        } else if (situacaoObra.getStatusObra().equals(StatusSituacaoObra.CONCLUIDA)) {
            concluirObra(dto);
        }
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoSituacaoObra arquivoEntity, SituacaoObra entity, ArquivoSituacaoObraDTO arquivoUpload) {
        arquivoEntity.setSituacaoObra(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    private void normalizarObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obra.setStatusObra(GeoobraStatusObra.EM_ANDAMENTO);

        if (Set.of(FaseGeoobraObra.FINALIZACAO, FaseGeoobraObra.ENTREGA, FaseGeoobraObra.INTERRUPCAO).contains(obra.getFase())) {
            obra.setFase(FaseGeoobraObra.MEDICAO);
        }

        obraRepository.save(obra);
    }

    private void interromperObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obra.setFase(FaseGeoobraObra.INTERRUPCAO);
        obra.setStatusObra(GeoobraStatusObra.INTERROMPIDA);
        obraRepository.save(obra);
    }

    private void atrasarObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obra.setStatusObra(GeoobraStatusObra.ATRASADA);

        if (Set.of(FaseGeoobraObra.FINALIZACAO, FaseGeoobraObra.ENTREGA, FaseGeoobraObra.INTERRUPCAO).contains(obra.getFase())) {
            obra.setFase(FaseGeoobraObra.MEDICAO);
        }

        obraRepository.save(obra);
    }

    private void paralisarObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obra.setStatusObra(GeoobraStatusObra.PARALISADA);

        if (Set.of(FaseGeoobraObra.FINALIZACAO, FaseGeoobraObra.ENTREGA, FaseGeoobraObra.INTERRUPCAO).contains(obra.getFase())) {
            obra.setFase(FaseGeoobraObra.MEDICAO);
        }

        obraRepository.save(obra);
    }

    private void reiniciarObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        GeoobraStatusObra newStatus = GeoobraStatusObra.NAO_INICIADA;
        FaseGeoobraObra faseGeoobraObra = obra.getFase();

        if (Set.of(FaseGeoobraObra.INICIAL, FaseGeoobraObra.MEDICAO, FaseGeoobraObra.FINALIZACAO).contains(faseGeoobraObra)) {
            newStatus = GeoobraStatusObra.EM_ANDAMENTO;
            if (FaseGeoobraObra.FINALIZACAO.equals(faseGeoobraObra)) {
                obra.setFase(FaseGeoobraObra.MEDICAO);
            }
        } else if (FaseGeoobraObra.ENTREGA.equals(faseGeoobraObra)) {
            newStatus = GeoobraStatusObra.FINALIZADA;
        }
        obra.setStatusObra(newStatus);

        obraRepository.save(obra);
    }

    private void concluirObra(SituacaoObraDTO dto) {
        GeoobraObra obra = obraService.getById(dto.getObra().getId());
        obra.setFase(FaseGeoobraObra.FINALIZACAO);
        obra.setStatusObra(GeoobraStatusObra.FINALIZADA);
        obraRepository.save(obra);
    }

    @Override
    public ArquivoSituacaoObraService getArquivoService() {
        return arquivoSituacaoObraService;
    }

    @Override
    public ArquivoSituacaoObraFileService getFileService() {
        return arquivoSituacaoObraFileService;
    }

    @Override
    public void validarArquivos(List<ArquivoSituacaoObraDTO> arquivos, StatusSituacaoObra statusObra, MotivoSituacaoObra motivo) throws AppException {
        List<TipoArquivoSituacaoObra> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios(statusObra, motivo));
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(StatusSituacaoObra statusObra, MotivoSituacaoObra motivo) throws AppException {
        List<String> filtros = new ArrayList<>();

        switch (statusObra) {
            case PARALISADA:
                switch (motivo) {
                    case GENERICA:
                        filtros.add("SO_PARALISADA_GENERICA");
                        break;
                    case RESCISAO_CONTRATUAL:
                        filtros.add("SO_PARALISADA_RESCISAO");
                        break;
                    case PERDA_VIGENCIA_CONTRATUAL:
                        filtros.add("SO_PARALISADA_PERDA_VIGENCIA");
                        break;
                    default:
                        break;
                }
                break;
            case CONCLUIDA:
                switch (motivo) {
                    case RECEBIDA_PROVISORIAMENTE:
                        filtros.add("SO_CONCLUIDA_PROVISORIAMENTE");
                        break;
                    case RECEBIDA_DEFINITIVO:
                        filtros.add("SO_CONCLUIDA_DEFINITIVO");
                        break;
                    default:
                        break;
                }
                break;
            case INTERROMPIDA:
                filtros.add("SO_INTERROMPIDA");
                break;
            default:
                break;
        }

        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = !filtros.isEmpty() ? this.obrigatoriedadeArquivoService.getArquivosObrigatorios("SITUACAO_OBRA", filtros) : List.of();
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    public ArquivoSituacaoObraToDtoMapper getMapper() {
        return arquivoSituacaoObraToDtoMapper;
    }

    @Override
    protected ArquivoSituacaoObra getNewArquivo() {
        return new ArquivoSituacaoObra();
    }

    @Override
    public SituacaoObra getUltimaSituacaoObra(Long obraId) {
        return this.repository.findTopByObraIdAndStatusNotOrderByIdDesc(obraId, StatusLicitacao.REMOVIDA).orElse(null);
    }

    @Override
    public boolean existeSituacaoObraEmRequisicaoModificao(Long obraId) {
        return this.repository.existsByObraIdAndIdRequisicaoModificacaoIsNotNullAndStatus(obraId, StatusLicitacao.PUBLICADA);
    }

    @Override
    public void removerArquivo(Long idEntity, Long idArquivo) throws AppException {
        if (!userCanAcess(idEntity)) {
            throw new AppException("Permissões insuficientes para remover o arquivo selecionado!", HttpStatus.UNAUTHORIZED);
        }

        ArquivoSituacaoObra arquivo = this.getArquivoService().getById(idArquivo);
        if (!idEntity.equals(arquivo.getIdEntidade())) {
            throw new AppException(String.format("Erro! Não foi possível remover. Arquivo não pertence à/ao %s!", getEntityName()), HttpStatus.UNPROCESSABLE_ENTITY);
        }

        this.getArquivoService().remover(idArquivo);
    }
}
