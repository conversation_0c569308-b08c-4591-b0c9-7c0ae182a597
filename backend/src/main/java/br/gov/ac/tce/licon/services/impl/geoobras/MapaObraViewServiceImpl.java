package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.DenunciaDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MapaObraViewFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.MapaObraView;
import br.gov.ac.tce.licon.repositories.geoobras.MapaObraViewRepository;
import br.gov.ac.tce.licon.services.geoobras.MapaObraViewService;
import br.gov.ac.tce.licon.services.impl.AbstractViewService;
import br.gov.ac.tce.licon.services.impl.geoobras.client.GeoobrasPublicoClient;
import br.gov.ac.tce.licon.services.specs.geoobras.MapaObraViewSpecification;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.errors.*;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

@Service
@Transactional
public class MapaObraViewServiceImpl extends
        AbstractViewService<MapaObraView, MapaObraViewFiltroRequest, MapaObraViewRepository>
        implements MapaObraViewService {

    @Autowired
    private MapaObraViewRepository repository;

    @Inject
    private MinioClient minioClientGeoobras;

    @Value("${spring.geoobras.minio.bucket}")
    private String bucketGeoobras;

    @Value("${spring.geoobras.mascara}")
    private String pathMascara;

    private static final Logger LOGGER = LoggerFactory.getLogger(MapaObraViewServiceImpl.class);

    @Override
    public MapaObraViewRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<MapaObraView> getSpecification(MapaObraViewFiltroRequest filtro) {
        return new MapaObraViewSpecification(filtro);
    }

    @Override
    public List<MapaObraView> getByObra(Long idObra) {
        return repository.getByObra(idObra);
    }
    
    @Override
    public String getMascara() {
        String result = "";
        try {
            InputStream inputStream = minioClientGeoobras.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketGeoobras)
                            .object(pathMascara)
                            .build());
            result = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException | InvalidKeyException | InvalidResponseException | XmlParserException |
                 InsufficientDataException | ErrorResponseException | NoSuchAlgorithmException | InternalException |
                 ServerException e) {
            String msg = "Houve algum erro ao tentar buscar a máscara no geoobras público.";
            LOGGER.error(msg, e);
        }
        return result;
    }

}
