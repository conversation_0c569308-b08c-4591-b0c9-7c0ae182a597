package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoDiarioObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoDiarioObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoDiarioObraFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.DiarioObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.DiarioObraFiltroRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoDiarioObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoDiarioObra;
import br.gov.ac.tce.licon.entities.geoobras.DiarioObra;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.DiarioObraRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoDiarioObraFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoDiarioObraService;
import br.gov.ac.tce.licon.services.geoobras.DiarioObraService;
import br.gov.ac.tce.licon.services.geoobras.GeoobraObraService;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.DiarioObraSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class DiarioObraServiceImpl extends AbstractUploadTipoServiceImpl<DiarioObra, DiarioObraFiltroRequest, DiarioObraRepository, ArquivoDiarioObraFileService, ArquivoDiarioObra, ArquivoDiarioObraFiltroRequest, ArquivoDiarioObraService, ArquivoDiarioObraDTO, ArquivoDiarioObraToDtoMapper, TipoArquivoDiarioObra> implements DiarioObraService {

    @Autowired
    private ArquivoDiarioObraService arquivoDiarioObraService;

    @Autowired
    private GeoobraObraService geoobraObraService;

    @Autowired
    private DiarioObraRepository repository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoDiarioObraToDtoMapper arquivoDiarioObraToDtoMapper;

    @Autowired
    private ArquivoDiarioObraFileService arquivoDiarioObraFileService;

    @Override
    public DiarioObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<DiarioObra> getSpecification(DiarioObraFiltroRequest filtro) {
        return new DiarioObraSpecification(filtro);
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<DiarioObra> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            DiarioObra diarioObra = entidadeOpt.get();
            validarRemover(diarioObra);
            List<ArquivoDiarioObra> arquivoDiarioObras = arquivoDiarioObraService.buscarPor(id);
            arquivoDiarioObras.forEach(arq -> arquivoDiarioObraService.remover(arq.getId()));
            getRepository().delete(diarioObra);
        }
    }

    @Override
    public BuscaResponse<DiarioObra> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().isEmpty()) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.DIARIO_OBRA.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoDiarioObra arquivoEntity, DiarioObra entity, ArquivoDiarioObraDTO arquivoUpload) {
        arquivoEntity.setDiario(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public ArquivoDiarioObraService getArquivoService() {
        return arquivoDiarioObraService;
    }

    @Override
    public ArquivoDiarioObraFileService getFileService() {
        return arquivoDiarioObraFileService;
    }

    @Override
    public ArquivoDiarioObraToDtoMapper getMapper() {
        return arquivoDiarioObraToDtoMapper;
    }

    @Override
    protected ArquivoDiarioObra getNewArquivo() {
        return new ArquivoDiarioObra();
    }

    @Override
    public void saveDiarioObra(DiarioObraDTO dto) {
        DiarioObra diario = dto.getDiarioObra();
        geoobraObraService.checkHasRequisicaoModificacaoObra(dto.getObra().getId());
        if (diario.getId() != null) {
            DiarioObra ultimoDiario = getUltimoDiarioObra(dto.getObra().getId());
            if (ultimoDiario != null && !ultimoDiario.getId().equals(diario.getId())) {
                throw new AppException("Não é possível editar um diário que não seja o último.", HttpStatus.BAD_REQUEST);
            }
        }

        List<ArquivoDiarioObraDTO> arquivos = dto.getArquivosDiarioObra();
        validarArquivos(arquivos);
        GeoobraObra obra = geoobraObraService.getById(dto.getObra().getId());
        diario.setObra(obra);
        diario = save(diario);
        saveArquivos(arquivos, diario);
    }

    @Override
    public DiarioObra getUltimoDiarioObra(Long obraId) {
        return this.repository.findTopByObraIdAndStatusNotOrderByIdDesc(obraId, StatusLicitacao.REMOVIDA).orElse(null);
    }

    @Override
    public Map<String, DiarioObra> getDiarioObraAnteriorPosterior(Long obraId, Long diarioId) {
        Optional<DiarioObra> anterior = this.repository.findTop1ByObraIdAndIdLessThanOrderByIdDesc(obraId, diarioId);
        Optional<DiarioObra> posterior = this.repository.findTop1ByObraIdAndIdGreaterThanOrderByIdAsc(obraId, diarioId);

        Map<String, DiarioObra> diarioAnteriorPosterior = new HashMap<>();
        diarioAnteriorPosterior.put("anterior", anterior.orElse(null));
        diarioAnteriorPosterior.put("posterior", posterior.orElse(null));

        return diarioAnteriorPosterior;
    }

    @Override
    public void removerArquivo(Long idEntity, Long idArquivo) throws AppException {
        if (!userCanAcess(idEntity)) {
            throw new AppException("Permissões insuficientes para remover o arquivo selecionado!", HttpStatus.UNAUTHORIZED);
        }

        ArquivoDiarioObra arquivo = this.getArquivoService().getById(idArquivo);
        if (!idEntity.equals(arquivo.getIdEntidade())) {
            throw new AppException(String.format("Erro! Não foi possível remover. Arquivo não pertence à/ao %s!", getEntityName()), HttpStatus.UNPROCESSABLE_ENTITY);
        }

        this.getArquivoService().remover(idArquivo);
    }

    @Override
    public boolean existeDiarioEmRequisicaoModificao(Long obraId) {
        return this.repository.existsByObraIdAndIdRequisicaoModificacaoIsNotNull(obraId);
    }
}

