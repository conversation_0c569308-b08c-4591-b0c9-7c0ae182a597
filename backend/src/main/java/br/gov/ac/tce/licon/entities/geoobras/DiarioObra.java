package br.gov.ac.tce.licon.entities.geoobras;

import br.gov.ac.tce.licon.entities.AbstractIdentificavel;
import br.gov.ac.tce.licon.entities.AbstractRequisicaoModificacaoIdentificavel;
import br.gov.ac.tce.licon.entities.LogicallyRemovable;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Audited(withModifiedFlag = true)
@AuditTable(value = "DIARIO_OBRA_AUD")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "GEOOBRAS", name = "DIARIO_OBRA")
@AttributeOverride(name = "id", column = @Column(name = "ID_DIARIO_OBRA"))
public class DiarioObra extends AbstractRequisicaoModificacaoIdentificavel implements LogicallyRemovable {

    @ManyToOne(targetEntity = GeoobraObra.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ID_OBRA")
    @JsonBackReference(value = "diarios")
    private GeoobraObra obra;

    @NotNull
    @Column(name = "OBSERVACOES")
    private String observacoes;

    @NotNull
    @Column(name = "DATA_INICIO_DIARIO")
    private LocalDate dataInicioDiario;

    @OneToMany(mappedBy = "diario", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    @NotAudited
    private List<ArquivoDiarioObra> arquivos;

    @NotNull
    @Column(name = "DATA_FIM_DIARIO")
    private LocalDate dataFimDiario;

    @Column(name = "STATUS")
    @Enumerated(value = EnumType.STRING)
    @NotNull
    private StatusLicitacao status;

    @Column(name = "ID_REQUISICAO_MODIFICACAO")
    private Long idRequisicaoModificacao;

    @Override
    public String titulo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        return String.format("Diário da Obra  %s de %s a %s", this.obra.toString(), this.dataInicioDiario.format(formatter), this.dataFimDiario.format(formatter));
    }
}
