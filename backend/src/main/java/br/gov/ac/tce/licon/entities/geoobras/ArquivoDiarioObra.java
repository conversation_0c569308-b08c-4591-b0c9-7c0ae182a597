package br.gov.ac.tce.licon.entities.geoobras;

import br.gov.ac.tce.licon.entities.ArquivoTipo;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoDiarioObra;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "GEOOBRAS", name = "ARQUIVO_DIARIO_OBRA")
@AttributeOverride(name = "id", column = @Column(name = "ID_ARQUIVO_DIARIO_OBRA"))
@AttributeOverride(name = "tipo", column = @Column(name = "TIPO_ARQUIVO_DIARIO_OBRA"))
@Audited(withModifiedFlag = true)
@AuditTable(value = "GEOOBRA_ARQUIVO_DIARIO_OBRA_AUD")
public class ArquivoDiarioObra extends ArquivoTipo<TipoArquivoDiarioObra> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_DIARIO_OBRA")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JsonIgnore
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private DiarioObra diario;
    
    @Override
    public Long getIdEntidade() {
        return diario.getId();
    }
}
