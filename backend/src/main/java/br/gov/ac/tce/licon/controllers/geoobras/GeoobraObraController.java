package br.gov.ac.tce.licon.controllers.geoobras;

import br.gov.ac.tce.licon.controllers.AbstractController;
import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoGeoobraObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivosGeoobraObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.GeoobraObraDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.GeoobraObraFiltroRequest;
import br.gov.ac.tce.licon.dtos.responses.GeoobraDetalheHistoricoDTO;
import br.gov.ac.tce.licon.dtos.responses.GeoobraHistoricoDTO;
import br.gov.ac.tce.licon.dtos.responses.cjurApi.PessoaVinculoDTO;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.GeoobraObraService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.websocket.server.PathParam;
import java.util.List;

@Tag(name = "Obra")
@RestController
@RequestMapping("/geo-obras/obras")
public class GeoobraObraController extends AbstractController<GeoobraObra, GeoobraObraFiltroRequest, GeoobraObraService> {

    @Autowired
    private GeoobraObraService service;

    @Override
    protected GeoobraObraService getService() {
        return service;
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/upload")
    public ResponseEntity<ArquivoDTO> upload(@RequestParam("file") MultipartFile file) throws AppException {
        ArquivoDTO response = service.upload(file);
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/download")
    public ResponseEntity<Resource> download(@PathParam("lookupId") String lookupId, @PathParam("nomeOriginal") String nomeOriginal, @PathParam("tipoArquivo") String tipoArquivo) throws AppException {
        ArquivoDTO arquivoDTO = new ArquivoDTO();
        arquivoDTO.setLookupId(lookupId);
        arquivoDTO.setNomeOriginal(nomeOriginal);
        arquivoDTO.setTipoArquivo(tipoArquivo);
        ArquivoBinarioDTO arquivoBinarioDTO = service.download(arquivoDTO);
        byte[] result = arquivoBinarioDTO.getBinario();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", arquivoBinarioDTO.getNomeOriginal()));

        ByteArrayResource resource = new ByteArrayResource(result);

        return ResponseEntity.ok().headers(headers).contentType(MediaType.parseMediaType(arquivoBinarioDTO.getTipoArquivo())).body(resource);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @DeleteMapping("/{idObra:[0-9]+}/arquivos/{idArquivo:[0-9]+}")
    public ResponseEntity<Void> removerArquivo(@PathVariable("idObra") Long idObra, @PathVariable("idArquivo") Long idArquivo) throws AppException {
        service.removerArquivo(idObra, idArquivo);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idObra:[0-9]+}/arquivos")
    public ResponseEntity<List<ArquivoGeoobraObraDTO>> recuperarArquivos(@PathVariable("idObra") Long idObra) throws AppException {
        List<ArquivoGeoobraObraDTO> resultado = service.recuperarArquivos(idObra);
        return ResponseEntity.ok(resultado);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PutMapping("/{idTdaLicitacao:[0-9]+}/arquivos/{idArquivo:[0-9]+}")
    public ResponseEntity<ArquivoGeoobraObraDTO> atualizarArquivo(@PathVariable("idObra") Long idObra, @PathVariable("idArquivo") Long idArquivo, @RequestBody @Valid ArquivoGeoobraObraDTO arquivoGeoobraObraDTO) throws AppException {
        arquivoGeoobraObraDTO.setIdArquivo(idArquivo);
        ArquivoGeoobraObraDTO resultado = service.atualizarArquivo(idObra, arquivoGeoobraObraDTO);
        return ResponseEntity.ok(resultado);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @PostMapping("/arquivos/validacao")
    public ResponseEntity<Void> validacaoArquivos(@Valid @RequestBody ArquivosGeoobraObraDTO dto) throws AppException {
        service.validarArquivos(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/salvar")
    public ResponseEntity<Void> salvarObra(@Valid @RequestBody GeoobraObraDTO dto) throws AppException {
        service.saveObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/iniciar")
    public ResponseEntity<Void> iniciarObra(@Valid @RequestBody GeoobraObraDTO dto) throws AppException {
        service.iniciarObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/entregar")
    public ResponseEntity<Void> entregarObra(@Valid @RequestBody GeoobraObraDTO dto) throws AppException {
        service.entregarObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idEntidade:[0-9]+}/gestores")
    public ResponseEntity<?> getGestores(@PathVariable("idEntidade") Long idEntidade) throws AppException {
        List<PessoaVinculoDTO> gestores = this.service.getGestores(idEntidade);
        return ResponseEntity.ok(gestores);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idEntidade:[0-9]+}/fiscais")
    public ResponseEntity<?> getFiscais(@PathVariable("idEntidade") Long idEntidade) throws AppException {
        List<PessoaVinculoDTO> fiscais = this.service.getFiscais(idEntidade);
        return ResponseEntity.ok(fiscais);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/historico/{idObra:[0-9]+}")
    public ResponseEntity<?> getHistorico(@PathVariable("idObra") Long idObra) throws AppException {
        List<GeoobraHistoricoDTO> historico = this.service.historicoObra(idObra);
        return ResponseEntity.ok(historico);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/historico/{idObra:[0-9]+}/detalhes/{idAudt:[0-9]+}")
    public ResponseEntity<?> getDetalhesHistorico(@PathVariable("idObra") Long idObra, @PathVariable("idAudt") Long idAudt) throws AppException {
        GeoobraDetalheHistoricoDTO historico = this.service.detalhesHistoricoObraByIdAudt(idAudt, idObra);
        return ResponseEntity.ok(historico);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idObra:[0-9]+}/has-requisicao-modificacao")
    public ResponseEntity<Boolean> hasRequisicaoModificao(@PathVariable("idObra") Long idObra) throws AppException {
        Boolean hasRequisicaoModificacao = this.service.hasRequisicaoModificacao(idObra);
        return ResponseEntity.ok(hasRequisicaoModificacao);
    }

}
