package br.gov.ac.tce.licon.entities;

import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.TipoAdjudicacao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Formula;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "CREDENCIADO_VIEW")
@AttributeOverride(name = "id", column = @Column(name = "ID_CREDENCIADO"))
public class CredenciadoView extends AbstractIdentificavel {

    @ManyToOne(targetEntity = Credenciamento.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_CREDENCIAMENTO")
    private Credenciamento credenciamento;

    @OneToMany(mappedBy = "credenciado", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "credenciado"}, allowSetters = true)
    @JsonManagedReference(value = "credenciadoItems")
    private List<CredenciadoItemView> credenciadoItems;

    @OneToOne(targetEntity = Licitante.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_LICITANTE")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    private Licitante licitante;

    @Column(name = "VIGENCIA_INICIAL")
    private LocalDate vigenciaInicial;

    @Column(name = "VIGENCIA_FINAL")
    private LocalDate vigenciaFinal;

    @Column(name = "STATUS")
    @Enumerated(value = EnumType.STRING)
    private StatusLicitacao status;

    @Column(name = "TIPO_ADJUDICACAO")
    @Enumerated(value = EnumType.STRING)
    private TipoAdjudicacao tipoAdjudicacao;

    @Column(name = "IS_SUSPENSO")
    private Boolean isSuspenso;

    @Column(name = "DATA_CADASTRO")
    private LocalDateTime dataCadastro;

    @Column(name = "SITUACAO")
    private String situacao;

    @Transient
    private Boolean hasContratoAssociado;

    @Column(name = "NUMERO_DOWNLOADS")
    private Long numeroDownloads;
}
