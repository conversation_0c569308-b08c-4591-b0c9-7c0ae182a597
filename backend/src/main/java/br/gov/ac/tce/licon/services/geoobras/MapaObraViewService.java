package br.gov.ac.tce.licon.services.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.DenunciaDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MapaObraViewFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.MapaObraView;
import br.gov.ac.tce.licon.services.IService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional
public interface MapaObraViewService extends IService<MapaObraView, MapaObraViewFiltroRequest> {
    List<MapaObraView> getByObra(Long idObra);

    String getMascara();
}
