package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.entities.Arquivo;
import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoGeoobraObra;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoMedicaoRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractFileServiceImpl;
import com.j256.simplemagic.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.math.BigInteger;
import java.sql.Timestamp;

@Service
public class ArquivoMedicaoFileServiceImpl extends AbstractFileServiceImpl implements ArquivoMedicaoFileService {

    private static final ContentType[] TIPOS_VALIDOS = new ContentType[]{ContentType.PDF, ContentType.MICROSOFT_EXCEL, ContentType.MICROSOFT_EXCEL_XML, ContentType.PNG, ContentType.JPEG, ContentType.MICROSOFT_WORD, ContentType.MICROSOFT_WORD_XML, ContentType.KML, ContentType.XML};

    @Inject
    private ArquivoMedicaoService arquivoMedicaoService;

    @Autowired
    private ArquivoMedicaoRepository arquivoMedicaoRepository;

    @Override
    protected Arquivo lookupArquivoParaDownload(Long idArquivo) throws AppException {
        try{
            return arquivoMedicaoService.getById(idArquivo);
        } catch (AppException e) {
            Object[] arquivoResult = arquivoMedicaoRepository.arquivoFromAudtTable(idArquivo);
            Object[] arquivo =  (Object[]) arquivoResult[0];
            Arquivo result = new ArquivoGeoobraObra();
            result.setId(((BigInteger) arquivo[0]).longValue());
            result.setDiretorio((String) arquivo[1]);
            result.setNome((String) arquivo[2]);
            result.setTipoArquivo((String) arquivo[3]);
            result.setDescricao((String) arquivo[4]);
            result.setDataEnvio(((Timestamp) arquivo[5]).toLocalDateTime());

            return result;
        }
    }

    @Override
    protected TipoEntidade getTipoEntidade() {
        return TipoEntidade.OBRA_MEDICAO;
    }

    protected ContentType[] tiposValidos() {
        return TIPOS_VALIDOS;
    }

}
