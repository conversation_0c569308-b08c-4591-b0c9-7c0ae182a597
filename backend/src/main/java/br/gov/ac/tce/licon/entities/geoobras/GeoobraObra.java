package br.gov.ac.tce.licon.entities.geoobras;

import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.geoobras.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.*;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Audited(withModifiedFlag = true)
@AuditTable(value = "GEOOBRAS_OBRA_AUD")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "GEOOBRAS", name = "OBRA")
@AttributeOverride(name = "id", column = @Column(name = "ID_OBRA"))
public class GeoobraObra extends AbstractRequisicaoModificacaoIdentificavel implements LogicallyRemovable {

    @ManyToOne(targetEntity = Contrato.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ID_CONTRATO")
    private Contrato contrato;

    @ManyToOne(targetEntity = ComplexoObra.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ID_COMPLEXO_OBRA")
    private ComplexoObra complexoObra;

    @ManyToOne(targetEntity = Entidade.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ID_ENTIDADE")
    private Entidade entidade;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "convenios")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<ConvenioObra> convenios;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "recursosProprios")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<RecursoProprioObra> recursosProprios;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    @NotAudited
    private List<ArquivoGeoobraObra> arquivos;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "diarios")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<DiarioObra> diarios;

    @NotNull
    @Column(name = "STATUS")
    @Enumerated(value = EnumType.STRING)
    private GeoobraStatusObra statusObra;

    @NotNull
    @Column(name = "FASE")
    @Enumerated(value = EnumType.STRING)
    private FaseGeoobraObra fase;

    @Column(name = "NUMERO")
    private BigDecimal numero;

    @NotAudited
    @Formula("(CASE WHEN ADMINISTRACAO_DIRETA = 1 AND NUMERO IS NOT NULL " +
            "THEN 'AD-' + RIGHT('0000' + CAST(CAST(NUMERO AS INT) AS VARCHAR(4)), 4) + '/' + CAST(YEAR(DATA_CADASTRO) AS VARCHAR(4)) " +
            "ELSE NULL END)")
    private String numeroFormatado;

    @NotAudited
    @Formula("(CASE WHEN ID_CONTRATO IS NOT NULL " +
            "THEN (SELECT c.NUMERO_CONTRATO FROM DBO.CONTRATO_VIEW c WHERE c.ID_CONTRATO = ID_CONTRATO) " +
            "ELSE NULL END)")
    private String numeroContrato;

    @NotAudited
    @Formula("(CASE WHEN ID_CONTRATO IS NOT NULL " +
            " THEN ( " +
            "   SELECT COALESCE(c.ADITIVO, c.VALOR_GLOBAL) " +
            "   FROM DBO.CONTRATO_VIEW c " +
            "   WHERE c.ID_CONTRATO = ID_CONTRATO" +
            " ) " +
            " ELSE NULL END)")
    private BigDecimal valorContrato;

    @NotAudited
    @Formula("(SELECT co.NOME FROM GEOOBRAS.COMPLEXO_OBRA co WHERE co.ID_COMPLEXO_OBRA = ID_COMPLEXO_OBRA)")
    private String nomeComplexoObra;

    @NotAudited
    @Formula("(SELECT STRING_AGG(e.ENDERECO, '|') FROM GEOOBRAS.ENDERECO_OBRA e WHERE e.ID_OBRA = ID_OBRA)")
    private String listaEnderecos;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "enderecos")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<EnderecoObra> enderecos;

    @NotNull
    @Column(name = "ADMINISTRACAO_DIRETA")
    private Boolean administracaoDireta;

    @Column(name = "VALOR")
    private BigDecimal valor;

    @NotNull
    @Column(name = "DESCRICAO")
    private String descricao;

    @NotNull
    @Column(name = "INCORPORAVEL_PATRIMONIO")
    private Boolean incorporavelPatrimonio;

    @NotNull
    @Column(name = "TIPO")
    @Enumerated(value = EnumType.STRING)
    private GeoobraTipoObra tipo;

    @NotNull
    @Column(name = "TIPO_DIMENSAO")
    @Enumerated(value = EnumType.STRING)
    private GeoobraTipoDimensaoObra tipoDimensao;

    @Column(name = "VALOR_DIMENSAO")
    private BigDecimal valorDimensao;

    @OneToMany(mappedBy = "obra", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "classificacoes")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<ClassificacaoObra> classificacoes;

    @Column(name = "SUBCLASSIFICACAO")
    @Enumerated(value = EnumType.STRING)
    private GeoobraSubclassificacaoObra subclassificacao;

    @Column(name = "SUBCLASSIFICACAO_OUTRO")
    private String subclassificacaoOutro;

    @NotNull
    @Column(name = "DATA_PREVISTA_INICIO")
    private LocalDate dataPrevistaInicio;

    @NotNull
    @Column(name = "DATA_PREVISTA_CONCLUSAO")
    private LocalDate dataPrevistaConclusao;

    @NotNull
    @Column(name = "DATA_EMISSAO_SERVICO")
    private LocalDate dataEmissaoServico;

    @NotNull
    @Column(name = "DATA_CADASTRO")
    private LocalDate dataCadastro;

    @NotNull
    @Column(name = "POSSUI_CONVENIOS_ASSOCIADOS")
    private Boolean possuiConveniosAssociados;

    @NotNull
    @Column(name = "POSSUI_RECURSOS_PROPRIOS")
    private Boolean possuiRecursosProprios = false;

    @NotNull
    @Column(name = "POSSUI_APENAS_ANTEPROJETO")
    private Boolean possuiApenasAnteprojeto;

    @Column(name = "DATA_INICIO")
    private LocalDate dataInicio;

    @Column(name = "DATA_FIM")
    private LocalDate dataFim;

    @Column(name = "DATA_CONCLUSAO")
    private LocalDate dataConclusao;

    @Column(name = "DATA_RECEBIMENTO")
    private LocalDate dataRecebimento;

    @Column(name = "TIPO_ENCERRAMENTO")
    @Enumerated(EnumType.STRING)
    private TipoEncerramento tipoEncerramento;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_MUNICIPIO", nullable = false)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Municipio municipio;

    @Column(name = "DESCRICAO_INTERRUPCAO")
    private String descricaoInterrupcao;

    @Column(name = "MOTIVO_INTERRUPCAO")
    @Enumerated(value = EnumType.STRING)
    private MotivoInterrupcaoObra motivoInterrupcao;

    @Column(name = "TIPO_GEOMETRIA")
    @Enumerated(value = EnumType.STRING)
    private TipoGeometriaObra tipoGeometriaObra;

    @NotNull
    @Column(name = "PREVISAO_CONCLUSAO")
    private String previsaoConclusao;
    
    @Column(name = "ID_GESTOR")
    private Long idGestor;

    @Column(name = "NOME_GESTOR")
    private String nomeGestor;

    @Column(name = "ID_FISCAL")
    private Long idFiscal;

    @Column(name = "NOME_FISCAL")
    private String nomeFiscal;

    @Column(name = "RESPONSAVEL_TECNICO_PROJETO")
    private String responsavelTecnicoProjeto;

    @Column(name = "NUMERO_ART_PROJETO")
    private String numeroArtProjeto;

    @Column(name = "RESPONSAVEL_TECNICO_EXECUCAO")
    private String responsavelTecnicoExecucao;

    @Column(name = "NUMERO_ART_EXECUCAO")
    private String numeroArtExecucao;

    @Column(name = "STATUS_PROCESSO")
    @Enumerated(value = EnumType.STRING)
    @NotNull
    private StatusLicitacao status;

    @Column(name = "ID_REQUISICAO_MODIFICACAO")
    private Long idRequisicaoModificacao;

    @Formula(value = "(CASE WHEN EXISTS(select 1 from GEOOBRAS.MEDICAO m where m.ID_OBRA = ID_OBRA) THEN 1 ELSE 0 END)")
    @NotAudited
    private Boolean hasMedicoes;

    @Formula(value = "(CASE WHEN EXISTS(" +
            "select 1 from GEOOBRAS.MEDICAO m where m.ID_OBRA = ID_OBRA AND m.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            " UNION " +
            "select 1 from GEOOBRAS.SITUACAO_OBRA so where so.ID_OBRA = ID_OBRA AND so.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            " UNION " +
            "select 1 from GEOOBRAS.DIARIO_OBRA do where do.ID_OBRA = ID_OBRA AND do.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            ") OR ID_REQUISICAO_MODIFICACAO IS NOT NULL THEN 1 ELSE 0 END)")
    @NotAudited
    private Boolean hasRequisicaoModificacao;

    @Override
    public String toString() {
        return this.administracaoDireta ? this.numeroFormatado : this.numeroContrato;
    }

    @Override
    public String titulo() {
        return String.format("%s - Obra n° %s", entidade.getNome(), this);
    }
}
