package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.DenunciaResponseDTO;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.DenunciaService;
import br.gov.ac.tce.licon.services.impl.geoobras.client.GeoobrasPublicoClient;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.errors.MinioException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class DenunciaServiceImpl implements DenunciaService {

    @Autowired
    private MinioClient minioClientGeoobras;

    @Value("${spring.geoobras.minio.bucket}")
    private String bucketGeoobras;


    @Autowired
    private GeoobrasPublicoClient geoobrasPublicoClient;

    private static final Logger LOGGER = LoggerFactory.getLogger(DenunciaServiceImpl.class);

    @Override
    public DenunciaResponseDTO getDenuncias(AdvancedSearchRequest filtro) {
        List<AdvancedSearchParameter> andParameters = filtro.getAndParameters();
        AdvancedSearchParameter obraParameter = getAdvancedSearchParameter(andParameters, "obraId", null).orElseThrow(() -> new IllegalArgumentException("O ID da Obra é necessário para a busca por denúncias."));
        AdvancedSearchParameter dataInicioParameter = getAdvancedSearchParameter(andParameters, "createdAt", SearchOperator.GREATER_THAN_EQUAL).orElse(null);
        AdvancedSearchParameter dataFimParameter = getAdvancedSearchParameter(andParameters, "createdAt", SearchOperator.LESSER_THAN_EQUAL).orElse(null);
        AdvancedSearchParameter queryParameter = getAdvancedSearchParameter(filtro.getOrParameters(), "query", null).orElse(null);
        Long obraId = Long.parseLong(obraParameter.getValue().toString());
        String query = queryParameter != null ? (String) queryParameter.getValue() : "";
        String dataInicio = dataInicioParameter != null ? (String) dataInicioParameter.getValue() : "";
        String dataFim = dataFimParameter != null ? (String) dataFimParameter.getValue() : "";

        return geoobrasPublicoClient.getDenuncias(obraId, filtro.getPage().getIndex(), filtro.getPage().getSize(), dataInicio, dataFim, query);
    }

    @Override
    public ArquivoBinarioDTO downloadArquivoDenuncia(ArquivoDTO arquivoDTO) {
        byte[] binario = downloadArquivoDenuncia(arquivoDTO.getLookupId());
        return ArquivoBinarioDTO.builder().binario(binario).nomeOriginal(arquivoDTO.getNomeOriginal()).build();
    }


    private byte[] downloadArquivoDenuncia(String path) throws AppException {

        try (InputStream inputStream = minioClientGeoobras.getObject(
                GetObjectArgs.builder().bucket(bucketGeoobras).object(path).build())) {
            return inputStream.readAllBytes();

        } catch (MinioException | IOException | InvalidKeyException | NoSuchAlgorithmException e) {
            e.printStackTrace();
            LOGGER.error(e.getMessage());
            return null;
        }
    }

    private static Optional<AdvancedSearchParameter> getAdvancedSearchParameter(List<AdvancedSearchParameter> parameters, String field, SearchOperator operator) {
        return parameters
                .stream()
                .filter(p -> p.getField().equals(field) && (operator == null || operator.equals(p.getOperator())))
                .findFirst();
    }
}
