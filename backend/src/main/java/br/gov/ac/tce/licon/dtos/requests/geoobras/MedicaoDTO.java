package br.gov.ac.tce.licon.dtos.requests.geoobras;

import br.gov.ac.tce.licon.entities.Entidade;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import lombok.Data;

import java.util.List;

@Data
public class MedicaoDTO {

    private Long id;

    private Medicao medicao;

    private Entidade entidade;

    private List<ArquivoMedicaoDTO> arquivosMedicao;
}
