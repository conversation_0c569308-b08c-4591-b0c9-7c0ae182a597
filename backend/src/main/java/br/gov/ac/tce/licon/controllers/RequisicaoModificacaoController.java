package br.gov.ac.tce.licon.controllers;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.entities.RequisicaoModificacao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.RequisicaoModificacaoService;
import org.json.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

@Tag(name = "RequisicaoModificacao")
@RestController
@RequestMapping("/requisicao-modificacao")
public class RequisicaoModificacaoController extends AbstractController<RequisicaoModificacao, RequisicaoModificacaoFiltroRequest, RequisicaoModificacaoService> {

    @Autowired
    private RequisicaoModificacaoService service;

    @Override
    protected RequisicaoModificacaoService getService() {
        return service;
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-contrato")
    public ResponseEntity<Void> requisicaoRemocaoContrato(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoContrato(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-aditivo-contrato")
    public ResponseEntity<Void> requisicaoRemocaoAditivoContrato(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoAditivoContrato(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-carona")
    public ResponseEntity<Void> requisicaoRemocaoCarona(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoCarona(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-dispensa")
    public ResponseEntity<Void> requisicaoRemocaoDispensa(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoDispensa(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-inexigibilidade")
    public ResponseEntity<Void> requisicaoRemocaoInexigibilidade(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoInexigibilidade(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-licitacao")
    public ResponseEntity<Void> requisicaoRemocaoLicitacao(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoLicitacao(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-reabertura-licitacao")
    public ResponseEntity<Void> requisicaoReaberturaLicitacao(@Valid @RequestBody RequisicaoReaberturaLicitacaoDTO requisicaoReaberturaDTO) throws AppException {
        this.service.requisitarReaberturaLicitacao(requisicaoReaberturaDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-credenciado")
    public ResponseEntity<Void> requisicaoRemocaoCredenciado(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoCredenciado(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idContrato:[0-9]+}/requisicao-modificacao-contrato")
    public ResponseEntity<Void> requisicaoModificacaoContrato(@PathVariable("idContrato") Long idContrato, @Valid @RequestBody RequisicaoModificacaoContratoDTO dto) throws AppException {
        dto.setIdContrato(idContrato);
        getService().requisicaoModificacaoContrato(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idContrato:[0-9]+}/requisicao-modificacao-rescisao-contratual")
    public ResponseEntity<Void> requisicaoModificacaoRescisaoContratual(@PathVariable("idContrato") Long idContrato, @Valid @RequestBody RequisicaoModificacaoContratoDTO dto) throws AppException {
        dto.setIdContrato(idContrato);
        getService().requisicaoModificacaoRescisaoContratual(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idLicitacao:[0-9]+}/requisicao-modificacao-licitacao")
    public ResponseEntity<Void> requisitarModificacaoLicitacao(@PathVariable("idLicitacao") Long idLicitacao, @Valid @RequestBody RequisicaoModificacaoLicitacaoDTO dto) throws AppException {
        dto.setIdLicitacao(idLicitacao);
        getService().requisitarModificacaoLicitacao(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idInexigibilidade:[0-9]+}/requisicao-modificacao-inexigibilidade/{lei}")
    public ResponseEntity<Void> requisitarModificacaoInexigibilidade(@PathVariable("idInexigibilidade") Long idInexigibilidade, @Valid @RequestBody RequisicaoModificacaoInexigibilidadeDTO dto, @PathVariable("lei") String lei) throws AppException {
        dto.setIdInexigibilidade(idInexigibilidade);
        getService().requisitarModificacaoInexigibilidade(dto, lei);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idAditivo:[0-9]+}/requisicao-modificacao-aditivo-contrato")
    public ResponseEntity<Void> requisicaoModificacaoAditivoContrato(@PathVariable("idAditivo") Long idAditivo, @Valid @RequestBody RequisicaoModificacaoAditivoContratoDTO dto) throws AppException {
        dto.setIdAditivo(idAditivo);
        getService().requisicaoModificacaoAditivoContrato(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idRequisicaoModificacao:[0-9]+}/modificacoes")
    public ResponseEntity<Map<String, Object>> getModificacoes(@PathVariable("idRequisicaoModificacao") Long idRequisicaoModificacao) throws AppException {
        JSONObject object = service.getModificacoes(idRequisicaoModificacao);
        return ResponseEntity.ok(object.toMap());
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idDispensa:[0-9]+}/requisicao-modificacao-dispensa/{lei}")
    public ResponseEntity<Void> requisicaoModificacaoDispensa(@PathVariable("idDispensa") Long idDispensa, @Valid @RequestBody RequisicaoModificacaoDispensaDTO dto) throws AppException {
        dto.setIdDispensa(idDispensa);
        getService().requisicaoModificacaoDispensa(dto, dto.getDispensaDTO().getLei());
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idCarona:[0-9]+}/requisicao-modificacao-carona")
    public ResponseEntity<Void> requisitarModificacaoCarona(@PathVariable("idCarona") Long idCarona, @Valid @RequestBody RequisicaoModificacaoCaronaDTO dto) throws AppException {
        dto.setIdCarona(idCarona);
        getService().requisitarModificacaoCarona(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idTermoReferencia:[0-9]+}/requisicao-modificacao-termo-referencia")
    public ResponseEntity<Void> requisitarModificacaoTermoReferencia(@PathVariable("idTermoReferencia") Long idTermoReferencia, @Valid @RequestBody RequisicaoModificacaoTermoReferenciaDTO dto) throws AppException {
        dto.setIdTermoReferencia(idTermoReferencia);
        getService().requisitarModificacaoTermoReferencia(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idObraMedicao:[0-9]+}/requisicao-modificacao-obra-medicao")
    public ResponseEntity<Void> requisitarModificacaoObraMedicao(@PathVariable("idObraMedicao") Long idObraMedicao, @Valid @RequestBody RequisicaoModificacaoObraMedicaoDTO dto) throws AppException {
        dto.setIdObraMedicao(idObraMedicao);
        getService().requisitarModificacaoObraMedicao(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getReadPermission(#this.this.class.name))")
    @GetMapping("/{idRequisicaoModificacao:[0-9]+}/download")
    public ResponseEntity<Resource> download(@PathVariable("idRequisicaoModificacao") Long idRequisicaoModificacao, @Valid ArquivoDTO arquivoDTO) throws AppException {
        ArquivoBinarioDTO arquivoBinarioDTO = service.download(idRequisicaoModificacao, arquivoDTO);
        byte[] result = arquivoBinarioDTO.getBinario();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", arquivoBinarioDTO.getNomeOriginal()));

        ByteArrayResource resource = new ByteArrayResource(result);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType(arquivoBinarioDTO.getTipoArquivo()))
                .body(resource);
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idCredenciamento:[0-9]+}/requisicao-modificacao-credenciamento")
    public ResponseEntity<Void> requisitarModificacaoCredenciamento(@PathVariable("idCredenciamento") Long idCredenciamento, @Valid @RequestBody RequisicaoModificacaoCredenciamentoDTO dto) throws AppException {
        dto.setIdCredenciamento(idCredenciamento);
        this.service.requisitarModificacaoCredenciamento(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-credenciamento")
    public ResponseEntity<Void> requisicaoRemocaoCredenciamento(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoCredenciamento(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idProcesso:[0-9]+}/requisicao-modificacao-anulacao-revogacao")
    public ResponseEntity<Void> requisicaoModificacaoAnulacaoRevogacao(@PathVariable("idProcesso") Long idProcesso, @Valid @RequestBody RequisicaoModificacaoAnulacaoRevogacaoDTO dto) throws AppException {
        dto.setIdProcesso(idProcesso);
        this.service.requisicaoModificacaoAnulacaoRevogacao(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idDiario:[0-9]+}/requisicao-modificacao-diario-obra")
    public ResponseEntity<Void> requisitarModificacaoDiarioObra(@PathVariable("idDiario") Long idDiario, @Valid @RequestBody RequisicaoModificacaoDiarioObraDTO dto) throws AppException {
        dto.setIdDiarioObra(idDiario);
        this.service.requisitarModificacaoDiarioObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-diario-obra")
    public ResponseEntity<Void> requisicaoRemocaoDiarioObra(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoDiarioObra(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idSituacaoObra:[0-9]+}/requisicao-modificacao-situacao-obra")
    public ResponseEntity<Void> requisitarModificacaoSituacaoObra(@PathVariable("idSituacaoObra") Long idSituacaoObra, @Valid @RequestBody RequisicaoModificacaoSituacaoObraDTO dto) throws AppException {
        dto.setIdSituacaoObra(idSituacaoObra);
        this.service.requisitarModificacaoSituacaoObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-situacao-obra")
    public ResponseEntity<Void> requisicaoRemocaoSituacaoObra(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoSituacaoObra(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idMedicao:[0-9]+}/requisicao-modificacao-medicao-obra")
    public ResponseEntity<Void> requisitarModificacaoMedicaoObra(@PathVariable("idMedicao") Long idMedicao, @Valid @RequestBody RequisicaoModificacaoMedicaoObraDTO dto) throws AppException {
        dto.setIdMedicao(idMedicao);
        this.service.requisitarModificacaoMedicaoObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idObra:[0-9]+}/requisicao-modificacao-obra")
    public ResponseEntity<Void> requisitarModificacaoObra(@PathVariable("idObra") Long idObra, @Valid @RequestBody RequisicaoModificacaoGeoobraObraDTO dto) throws AppException {
        dto.setIdObra(idObra);
        this.service.requisitarModificacaoGeoobraObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/requisicao-remocao-obra")
    public ResponseEntity<Void> requisicaoRemocaoObra(@Valid @RequestBody RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        this.service.requisicaoRemocaoGeoobraObra(requisicaoRemocaoDTO);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idObra:[0-9]+}/requisicao-modificacao-inicio-obra")
    public ResponseEntity<Void> requisitarModificacaoInicioObra(@PathVariable("idObra") Long idObra, @Valid @RequestBody RequisicaoModificacaoGeoobraObraDTO dto) throws AppException {
        dto.setIdObra(idObra);
        this.service.requisitarModificacaoInicioObra(dto);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority(@userPermissionService.getWritePermission(#this.this.class.name))")
    @PostMapping("/{idObra:[0-9]+}/requisicao-modificacao-entrega-obra")
    public ResponseEntity<Void> requisitarModificacaoEntregaObra(@PathVariable("idObra") Long idObra, @Valid @RequestBody RequisicaoModificacaoGeoobraObraDTO dto) throws AppException {
        dto.setIdObra(idObra);
        this.service.requisitarModificacaoEntregaObra(dto);
        return ResponseEntity.ok().build();
    }
}
