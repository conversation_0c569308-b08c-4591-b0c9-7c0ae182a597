package br.gov.ac.tce.licon.services;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import org.json.JSONObject;

public interface RequisicaoModificacaoService extends IService<RequisicaoModificacao, RequisicaoModificacaoFiltroRequest> {

    void requisicaoRemocaoContrato(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoRemocaoAditivoContrato(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisitarModificacaoLicitacao(RequisicaoModificacaoLicitacaoDTO dto) throws AppException;

    void requisitarModificacaoInexigibilidade(RequisicaoModificacaoInexigibilidadeDTO dto, String lei) throws AppException;

    void requisicaoModificacaoContrato(RequisicaoModificacaoContratoDTO requisicaoModificacaoContratoDTO) throws AppException;

    void requisicaoModificacaoRescisaoContratual(RequisicaoModificacaoContratoDTO requisicaoModificacaoContratoDTO) throws AppException;

    void requisicaoModificacaoAditivoContrato(RequisicaoModificacaoAditivoContratoDTO requisicaoModificacaoAditivoContratoDTO) throws AppException;

    void julgamentoAuditorRequisicao(RequisicaoModificacaoJulgamentoAuditorDTO dto) throws AppException;

    void requisicaoModificacaoDispensa(RequisicaoModificacaoDispensaDTO requisicaoModificacaoDispensaDTO, String lei) throws AppException;

    void requisitarModificacaoCarona(RequisicaoModificacaoCaronaDTO dto) throws AppException;

    void requisicaoRemocaoCarona(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoRemocaoDispensa(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoRemocaoInexigibilidade(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoRemocaoLicitacao(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisitarModificacaoTermoReferencia(RequisicaoModificacaoTermoReferenciaDTO dto) throws AppException;

    void requisitarReaberturaLicitacao(RequisicaoReaberturaLicitacaoDTO dto) throws AppException;

    void requisitarModificacaoObraMedicao(RequisicaoModificacaoObraMedicaoDTO dto) throws AppException;

    void requisitarModificacaoCredenciamento(RequisicaoModificacaoCredenciamentoDTO dto) throws AppException;

    void requisicaoRemocaoCredenciamento(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoRemocaoCredenciado(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisicaoModificacaoAnulacaoRevogacao(RequisicaoModificacaoAnulacaoRevogacaoDTO dto) throws AppException;

    void requisitarModificacaoDiarioObra(RequisicaoModificacaoDiarioObraDTO dto) throws AppException;

    void requisicaoRemocaoDiarioObra(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisitarModificacaoGeoobraObra(RequisicaoModificacaoGeoobraObraDTO dto) throws AppException;

    void requisicaoRemocaoGeoobraObra(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisitarModificacaoInicioObra(RequisicaoModificacaoGeoobraObraDTO dto) throws AppException;
    
    void requisitarModificacaoEntregaObra(RequisicaoModificacaoGeoobraObraDTO dto) throws AppException;
    
    void requisitarModificacaoSituacaoObra(RequisicaoModificacaoSituacaoObraDTO dto) throws AppException;

    void requisicaoRemocaoSituacaoObra(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException;

    void requisitarModificacaoMedicaoObra(RequisicaoModificacaoMedicaoObraDTO dto) throws AppException;

    JSONObject getModificacoes(Long idRequisicaoModificacao) throws AppException;

    ArquivoBinarioDTO download(Long idReqMod, ArquivoDTO arquivoDTO) throws AppException;
}
