package br.gov.ac.tce.licon.repositories.geoobras;

import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.geoobras.*;
import br.gov.ac.tce.licon.repositories.IRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;


import java.util.List;
import java.util.Optional;

public interface SituacaoObraRepository extends IRepository<SituacaoObra> {
    Optional<SituacaoObra> findTopByObraIdAndStatusNotOrderByIdDesc(Long obraId, StatusLicitacao status);

    @Query("SELECT s " +
            "FROM SituacaoObra s JOIN FETCH s.obra " +
            "WHERE s.obra.id IN :obraIds " +
            "  AND MONTH(s.dataCadastro) = :mes " +
            "  AND YEAR(s.dataCadastro) = :ano")
    List<SituacaoObra> findAllByObraIdsAndMesAndAno(
            @Param("obraIds") List<Long> obraIds,
            @Param("mes") int mes,
            @Param("ano") int ano
    );

    boolean existsByObraIdAndIdRequisicaoModificacaoIsNotNullAndStatus(
            Long obraId,
            StatusLicitacao status
    );
}

