package br.gov.ac.tce.licon.entities.geoobras;

import br.gov.ac.tce.licon.entities.AbstractIdentificavel;
import br.gov.ac.tce.licon.entities.Contrato;
import br.gov.ac.tce.licon.entities.Entidade;
import br.gov.ac.tce.licon.entities.enums.geoobras.FaseGeoobraObra;
import br.gov.ac.tce.licon.entities.enums.geoobras.GeoobraStatusObra;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoGeometriaObra;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "GEOOBRAS", name = "ACOMPANHAMENTO_OBRA_VIEW")
@AttributeOverride(name = "id", column = @Column(name = "ID_OBRA"))
public class AcompanhamentoObraView extends AbstractIdentificavel {

    @Column(name = "NUMERO")
    private BigDecimal numero;

    @Column(name = "DESCRICAO")
    private String descricao;

    @Column(name = "VALOR")
    private BigDecimal valor;

    @Column(name = "STATUS")
    @Enumerated(value = EnumType.STRING)
    private GeoobraStatusObra status;

    @Column(name = "FASE")
    @Enumerated(value = EnumType.STRING)
    private FaseGeoobraObra fase;

    @Column(name = "PERCENTUAL_CONCLUSAO")
    private BigDecimal percentualConclusao;

    @Column(name = "ULTIMA_MEDICAO")
    private LocalDate ultimaMedicao;

    @Column(name = "DATA_INICIO")
    private LocalDate dataInicio;

    @Column(name = "DATA_FIM")
    private LocalDate dataFim;

    @Column(name = "TIPO_GEOMETRIA")
    @Enumerated(value = EnumType.STRING)
    private TipoGeometriaObra tipoGeometriaObra;

    @Column(name = "ADMINISTRACAO_DIRETA")
    private Boolean administracaoDireta;

    @ManyToOne(targetEntity = Entidade.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_ENTIDADE")
    private Entidade entidade;

    @ManyToOne(targetEntity = Contrato.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_CONTRATO")
    private Contrato contrato;

    @Formula("(CASE WHEN ADMINISTRACAO_DIRETA = 1 AND NUMERO IS NOT NULL " +
            "THEN 'AD-' + RIGHT('0000' + CAST(CAST(NUMERO AS INT) AS VARCHAR(4)), 4) + '/' + CAST(YEAR(DATA_CADASTRO) AS VARCHAR(4)) " +
            "ELSE NULL END)")
    private String numeroFormatado;

    @Formula("(CASE WHEN ID_CONTRATO IS NOT NULL " +
            "THEN (SELECT c.NUMERO_CONTRATO FROM DBO.CONTRATO_VIEW c WHERE c.ID_CONTRATO = ID_CONTRATO) " +
            "ELSE NULL END)")
    private String numeroContrato;

    @Formula("(CASE WHEN ID_CONTRATO IS NOT NULL " +
            " THEN ( " +
            "   SELECT COALESCE(c.ADITIVO, c.VALOR_GLOBAL) " +
            "   FROM DBO.CONTRATO_VIEW c " +
            "   WHERE c.ID_CONTRATO = ID_CONTRATO" +
            " ) " +
            " ELSE NULL END)")
    private BigDecimal valorContrato;

    @Formula("(SELECT co.NOME FROM GEOOBRAS.COMPLEXO_OBRA co WHERE co.ID_COMPLEXO_OBRA = ID_COMPLEXO_OBRA)")
    private String nomeComplexoObra;

    @OneToOne(targetEntity = SituacaoObra.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_SITUACAO_OBRA")
    private SituacaoObra situacaoObra;

    @Column(name = "ID_REQUISICAO_MODIFICACAO")
    private Long idRequisicaoModificacao;

    @Formula(value = "(CASE WHEN EXISTS(" +
            "select 1 from GEOOBRAS.MEDICAO m where m.ID_OBRA = ID_OBRA AND m.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            " UNION " +
            "select 1 from GEOOBRAS.SITUACAO_OBRA so where so.ID_OBRA = ID_OBRA AND so.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            " UNION " +
            "select 1 from GEOOBRAS.DIARIO_OBRA do where do.ID_OBRA = ID_OBRA AND do.ID_REQUISICAO_MODIFICACAO IS NOT NULL" +
            ") OR ID_REQUISICAO_MODIFICACAO IS NOT NULL THEN 1 ELSE 0 END)")
    private Boolean hasRequisicaoModificacao;
}
