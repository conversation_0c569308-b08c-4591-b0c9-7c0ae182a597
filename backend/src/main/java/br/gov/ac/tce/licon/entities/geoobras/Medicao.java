package br.gov.ac.tce.licon.entities.geoobras;

import br.gov.ac.tce.licon.entities.AbstractIdentificavel;
import br.gov.ac.tce.licon.entities.AbstractRequisicaoModificacaoIdentificavel;
import br.gov.ac.tce.licon.entities.EmpenhoContrato;
import br.gov.ac.tce.licon.entities.LogicallyRemovable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Audited(withModifiedFlag = true)
@AuditTable(value = "GEOOBRAS_MEDICAO_AUD")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "GEOOBRAS", name = "MEDICAO")
@AttributeOverride(name = "id", column = @Column(name = "ID_MEDICAO"))
public class Medicao extends AbstractRequisicaoModificacaoIdentificavel {

    @ManyToOne(targetEntity = GeoobraObra.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_OBRA")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private GeoobraObra obra;

    @ManyToOne(targetEntity = EmpenhoContrato.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_EMPENHO")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private EmpenhoContrato empenho;

    @NotNull
    @Column(name = "VALOR")
    private BigDecimal valor;

    @NotAudited
    @Formula("(SELECT vm.PERCENTUAL_CONCLUSAO FROM GEOOBRAS.VW_MEDICAO_PERCENTUAL_OBRA vm WHERE vm.ID_MEDICAO = ID_MEDICAO)")
    private BigDecimal percentualConclusao;

    @NotNull
    @Column(name = "DATA_INICIO")
    private LocalDate dataInicio;

    @NotNull
    @Column(name = "DATA_FIM")
    private LocalDate dataFim;

    @NotNull
    @Column(name = "DATA_CADASTRO")
    private LocalDate dataCadastro;

    @Column(name = "VALOR_CONTRATO_ALTERADO")
    private Boolean valorContratoAlterado;

    @Column(name = "PROJETO_ALTERADO")
    private Boolean projetoAlterado;

    @Column(name = "ID_REQUISICAO_MODIFICACAO")
    private Long idRequisicaoModificacao;

    @Override
    public String toString() {
        return getId().toString();
    }

    @Override
    public String titulo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        return String.format("Medição da Obra  %s de %s a %s", this.obra.toString(), this.dataInicio.format(formatter), this.dataFim.format(formatter));
    }
}
