package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoGeoobraObraFiltroRequest;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoGeoobraObra;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoMedicao;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoGeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoMedicao;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoGeoobraObraRepository;
import br.gov.ac.tce.licon.repositories.geoobras.GeoobraObraRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoGeoobraObraService;
import br.gov.ac.tce.licon.services.geoobras.GeoobraObraService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoGeoobraObraSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class ArquivoGeoobraObraServiceImpl
        extends AbstractService<ArquivoGeoobraObra, ArquivoGeoobraObraFiltroRequest, ArquivoGeoobraObraRepository>
        implements ArquivoGeoobraObraService {

    @Autowired
    private ArquivoGeoobraObraRepository repository;

    @Autowired
    private GeoobraObraRepository geoobraObraRepository;

    @Override
    public ArquivoGeoobraObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoGeoobraObra> getSpecification(ArquivoGeoobraObraFiltroRequest filtro) {
        return new ArquivoGeoobraObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoGeoobraObra entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
        if (entity.getFase() == null) {
            entity.setFase(entity.getObra().getFase());
        }
    }

    @Override
    public List<ArquivoGeoobraObra> buscarPor(Long idObra) {
        return repository.buscarPor(idObra);
    }

    public ArquivoGeoobraObra fromColumnsMap(Map<String, Object> map) {
        ArquivoGeoobraObra result = new ArquivoGeoobraObra();
        result.setObra(geoobraObraRepository.getById((Long) map.get("ID_OBRA")));
        result.setDescricao((String) map.get("DESCRICAO"));
        result.setTipo(TipoArquivoGeoobraObra.valueOf((String) map.get("TIPO_ARQUIVO_LICON")));
        result.setId((Long) map.get("ID_ARQUIVO_OBRA"));
        result.setNome((String) map.get("NOME"));
        result.setDataEnvio(((Timestamp) map.get("DATA_ENVIO")).toLocalDateTime());
        result.setDiretorio((String) map.get("DIRETORIO"));
        result.setTipoArquivo((String) map.get("TIPO_ARQUIVO"));
        return result;
    }
}
