package br.gov.ac.tce.licon.repositories.geoobras;

import br.gov.ac.tce.licon.entities.enums.geoobras.GeoobraStatusObra;
import br.gov.ac.tce.licon.entities.geoobras.ClassificacaoObra;
import br.gov.ac.tce.licon.entities.geoobras.EnderecoObra;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.repositories.IRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface GeoobraObraRepository extends IRepository<GeoobraObra>, GeoobraObraRepositoryCustom {

    @Query("SELECT o FROM GeoobraObra o " +
            "INNER JOIN FETCH o.entidade e " +
            "INNER JOIN FETCH e.ente " +
            "LEFT JOIN FETCH o.contrato c " +
            "INNER JOIN FETCH o.municipio m " +
            "LEFT JOIN FETCH c.contratoLicitante cl " +
            "LEFT JOIN FETCH cl.licitante l " +
            "where o.statusObra != 'FINALIZADA'")
    List<GeoobraObra> findObrasCompletasForSynchronization();

    @Query("SELECT e FROM EnderecoObra e WHERE e.obra.id = ?1")
    List<EnderecoObra> findEnderecosByIdObraForSynchronization(Long idObra);

    @Query("SELECT c FROM ClassificacaoObra c WHERE c.obra.id = ?1")
    List<ClassificacaoObra> findClassificacaoObraByIdObraForSynchronization(Long idObra);

    @Query(value = "select ID_AUDT, REVTYPE, r.[DATA], r.LOGIN_USUARIO, 'OBRA' as TIPO from AUDITORIA.GEOOBRAS_OBRA_AUD goa JOIN AUDITORIA.REVINFO r ON r.ID_AUDITORIA = goa.ID_AUDT WHERE goa.ID_OBRA = ?1", nativeQuery = true)
    List<Object[]> findHistoricoAuditoriaObra(Long idObra);

    @Query(value = "SELECT ID_AUDT, REVTYPE, DATA, LOGIN_USUARIO, TIPO FROM HISTORICO_AUDITORIA_OBRA_VIEW WHERE ID_OBRA = ?1", nativeQuery = true)
    List<Object[]> findHistoricoAuditoriaRelacionamentosObra(Long idObra);
    
    @Query("SELECT o FROM GeoobraObra o " +
            " LEFT JOIN FETCH o.contrato c" +
            " WHERE o.statusObra = 'EM_ANDAMENTO'" +
            "   AND (" +
            "     (o.administracaoDireta = true AND o.dataPrevistaConclusao < CURRENT_TIMESTAMP)" +
            "     OR" +
            "     (o.administracaoDireta = false AND c.dataFinalVigente < CURRENT_TIMESTAMP)" +
            "   )"
    )
    List<GeoobraObra> findObrasEmAndamentoComPrazoVencido();

    @Query("SELECT DISTINCT o " +
            "FROM GeoobraObra o " +
            " LEFT JOIN FETCH o.contrato c " +
            "WHERE o.statusObra = 'ATRASADA' " +
            "  AND ( " +
            "    (o.administracaoDireta = true  AND o.dataPrevistaConclusao >= CURRENT_TIMESTAMP) " +
            " OR (o.administracaoDireta = false AND c.dataFinalVigente >= CURRENT_TIMESTAMP) " +
            "  ) " +
            "  AND EXISTS ( " +
            "      SELECT 1 " +
            "      FROM SituacaoObra s " +
            "      WHERE s.obra = o " +
            "        AND s.definidoSistema = true " +
            "        AND s.dataCadastro = ( " +
            "            SELECT MAX(s2.dataCadastro) " +
            "            FROM SituacaoObra s2 " +
            "            WHERE s2.obra = o" +
            "          )" +
            "  )"
    )
    List<GeoobraObra> findObrasAtrasadasDefinidoPeloSistemaComPrazoRegularizado();

    @Modifying
    @Query("UPDATE GeoobraObra o SET o.statusObra = :novoStatus WHERE o.id IN :obraIds")
    int updateStatusForObras(@Param("novoStatus") GeoobraStatusObra novoStatus, @Param("obraIds") List<Long> obraIds);

}
